# Technology Stack Research - 2025 Dashboard

**Date:** August 2, 2025

This document surveys choices for a real-time dashboard application built with React + TypeScript. It covers project setup, frameworks, state management, styling, data layer, real-time transport, animation, and LLM/Web-search integrations.

---

## 1. React + TypeScript Project Setup

**Options & Best Practices:**

- **Folder Structure**: `src/` with `components/`, `hooks/`, `services/`, `state/`, `styles/`, `utils/`.
- **Tooling**:
  - **ESLint** with `@typescript-eslint` + React plugin
  - **Prettier** for code formatting
  - **Vitest** for unit tests + `@testing-library/react`
- **TSConfig**:
  ```json
  {
    "compilerOptions": {
      "strict": true,
      "forceConsistentCasingInFileNames": true,
      "skipLibCheck": true,
      "moduleResolution": "node",
      "jsx": "react-jsx"
    }
  }
  ```
- **Path Aliases**: configure `tsconfig.json` with `@components/*`, `@utils/*` for readability.

**Performance Considerations:**
- Code splitting via React.lazy & Suspense
- Bundle analysis (e.g., `rollup-plugin-visualizer` in Vite)

---

## 2. Vite vs Next.js vs Remix

| Feature               | Vite (SPAs)                                   | Next.js (SSG/SSR)                             | Remix (SSR-first)                          |
|-----------------------|-----------------------------------------------|-----------------------------------------------|---------------------------------------------|
| Real-time updates     | Client-only; integrate WebSocket manually     | API routes + `getServerSideProps` + SSE support| Built-in loader/actions + streaming render |
| Data-fetching         | Fetch-only; no opinion                       | `getStaticProps`/`getServerSideProps`         | `loaders` and `actions`                     |
| Routing               | File-based via `vite-plugin-pages`            | File-based under `/app` folder                | File-based with nested layouts              |
| Deployment            | Static export or SSR via SSR adapter          | Vercel optimized                              | Any Node, Cloudflare, Deno                  |
| Pros                  | Fast HMR, lightweight                         | Ecosystem + analytics + Image Optimization    | First-party focus on UX & caching           |
| Cons                  | DIY conventions                               | Larger bundle; licensing under CC             | Younger community; fewer plugins            |

**Recommendation:** For full control and minimal overhead, Vite + React 19 with SSR adapter (e.g., `vite-ssr`) is ideal. Use Next.js if you need hybrid SSG/SSR and Vercel features; choose Remix for advanced streaming UX.

---

## 3. State Management for Real-Time Dashboards

### Zustand
```ts
import create from 'zustand';
const useStore = create<{ count: number; increment: ()=>void }>(set => ({
  count: 0,
  increment: () => set(state => ({ count: state.count + 1 })),
}));
```
- Pros: Minimal bundle (≈1KB), easy API, middleware (persist, subscribe).
- Cons: No built-in selectors; boilerplate for complex slicing.

### Jotai
```ts
import { atom } from 'jotai';
const countAtom = atom(0);
const doubleAtom = atom(get => get(countAtom) * 2);
```
- Pros: Fine-grained updates, React Suspense support.
- Cons: Steeper learning curve, larger (~2.5KB).

### Valtio
```ts
import { proxy } from 'valtio';
const state = proxy({ todos: [] as string[] });
```
- Pros: Proxy-based, mutable API, easy to pick deep updates.
- Cons: Magic proxy can complicate debugging.

**Recommendation:** Use Zustand for most use cases; integrate Jotai for Suspense-driven data or Valtio for complex nested state.

---

## 4. Styling & Animations

### CSS-in-JS (Emotion)
```tsx
/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
<div css={css`transition: opacity 0.2s;`} />
```
- Pros: Theming, dynamic styles, SSR-friendly.
- Cons: Runtime cost, larger bundle.

### Tailwind CSS
```html
<div class="transition-opacity duration-200 hover:opacity-80" />
```
- Pros: Utility-first, JIT engine, consistent design tokens.
- Cons: Class name verbosity; requires purge config.

### CSS Modules
```css
/* Button.module.css */
.btn { transition: all .2s; }
```
- Pros: Scoped styles, no runtime.
- Cons: No theming, global/shared utilities need imports.

**Recommendation:** Tailwind CSS + `@tailwindcss/forms` + `@tailwindcss/typography` for heavy animations, with `animate.css` or utility animations.

---

## 5. Database Solutions

| Database            | Pros                                                | Cons                                |
|---------------------|-----------------------------------------------------|-------------------------------------|
| PostgreSQL + Prisma | Mature, relational, powerful migrations, TS types   | Requires DBA; infra overhead        |
| Supabase            | Hosted Postgres + Auth + Storage + Realtime         | Vendor lock-in; cost scales         |
| PocketBase          | Embedded Go-based, real-time, file storage          | Less relational; community smaller  |

**Recommendation:** Start with Supabase for rapid prototyping, migrate to custom Postgres + Prisma if need fine-grained control.

---

## 6. Real-Time Transport

- **WebSocket**: Bi-directional, low-latency; server must manage socket pool.
- **Server-Sent Events (SSE)**: Uni-directional (server→client), auto-reconnect.
- **Polling**: Simplest; heavy on request volume.

**Recommendation:** WebSocket via `socket.io` or `ws` for interactive dashboards; use SSE for low-traffic streams (e.g., notifications).

---

## 7. Animation Libraries

| Library         | Pros                                   | Cons                         |
|-----------------|----------------------------------------|------------------------------|
| Framer Motion  (v8) | Layout animations, shared layout, SSR | Bundle ~20KB gzipped         |
| React Spring     | Physics-based, declarative springs    | More verbose API             |
| Auto-Animate     | Zero-config auto-animations            | Less control; DOM diff based |

**Recommendation:** Framer Motion for production-grade UI; supplement with Auto-Animate for lists.

---

## 8. OpenRouter API

**Docs & Integration**: https://openrouter.ai/docs

```ts
import OpenRouter from 'openrouter';
const client = new OpenRouter({ apiKey: process.env.OPENROUTER_KEY });
const completion = await client.chat.completions.create({
  model: 'gpt-4o', messages: [...] });
```
- Pros: Multi-model support; streaming.
- Cons: New API surface; usage cost.

---

## 9. Mirascope (LLM Functions)

**Docs**: https://mirascope.ai/docs

```ts
import { createFunction } from 'mirascope';
const fn = createFunction('getWeather', schema, handler);
```
- Pros: Typed functions, orchestration support.
- Cons: Early stage; limited examples.

---

## 10. Langsearch API

**Docs**: https://docs.langsearch.com

```ts
import LangSearch from 'langsearch';
const ls = new LangSearch({ endpoint: '/api/search' });
const results = await ls.search({ query: 'dashboard metrics' });
```
- Pros: Unified vector + web search.
- Cons: Subscription cost; embedding limits.

---

*End of Report*

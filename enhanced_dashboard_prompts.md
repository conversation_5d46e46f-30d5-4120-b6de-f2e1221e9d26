# AI Dashboard App - Enhanced Incremental Development Prompts

## App Architecture Overview (READ THIS FIRST)

You are building an intelligent AI dashboard application with these core features:

**MAIN INTERFACE:**
- Single input bar where users type commands
- Response bar below showing AI processing with animations
- Task list component (sortable, real-time updates)
- Calendar component (shows events/reminders)
- Recent searches box (shows last 10 searches)

**CORE FLOWS:**
1. **TASK FLOW**: "Remember to do biology homework" → AI categorizes → Creates task → Updates task list → Natural language response
2. **CALENDAR FLOW**: "Submit math assignment on 5 August" → AI categorizes → Creates event → Updates calendar → Natural language response  
3. **SEARCH FLOWS**: 
   - Database search: "How many tasks do I have?" → Semantic search of user data
   - Web search: "Best mouse 2025?" → Web search via Langsearch API

**TECHNICAL STACK:**
- Frontend: React + TypeScript + Vite + Tailwind + Framer Motion + Zustand
- Backend: Node.js/Express with PostgreSQL + Prisma
- AI: OpenRouter LLM (free tier) + Mirascope + custom tool calling workaround
- Search: Langsearch API for database and web search
- Real-time: WebSocket/SSE for live updates

**CRITICAL REQUIREMENTS:**
- All AI interactions show step-by-step animated feedback
- No hardcoded categorization - pure AI classification
- Streaming responses with typewriter effect
- Real-time updates across all components
- Complete persistence across sessions

---

## Phase 1: Project Setup and Infrastructure

### Research Prompt 1.1 - Technology Stack Research
```
Using context7 MCP and fetch tool, research extensively:
1. Latest best practices for React + TypeScript project setup in 2025
2. Vite vs Next.js vs Remix for a dashboard application with real-time updates
3. State management solutions (Zustand, Jotai, Valtio) for real-time dashboard updates
4. CSS-in-JS vs Tailwind CSS vs CSS Modules for animation-heavy interfaces
5. Best database solutions for task/calendar/search history (PostgreSQL + Prisma vs Supabase vs PocketBase)
6. WebSocket vs Server-Sent Events vs polling for real-time updates
7. Animation libraries: Framer Motion vs React Spring vs Auto-Animate
8. OpenRouter API documentation and integration patterns
9. Mirascope library documentation for LLM functions
10. Langsearch API documentation for database and web search capabilities

Save all findings to /research/01-technology-stack.md with:
- Pros/cons of each option
- Code examples
- Performance considerations
- Best practices for 2025
```

### Main Prompt 1.1 - Project Initialization
```
**CONTEXT: AI Dashboard App - Phase 1 Setup**
You are starting the AI Dashboard project from scratch. This is an intelligent dashboard where users type commands in a single input bar, and an AI system categorizes them as TASKS, CALENDAR EVENTS, or QUESTIONS, then executes appropriate actions with animated feedback.

**PREREQUISITES:**
1. MUST read /research/01-technology-stack.md completely before proceeding
2. This is the foundation phase - no previous code exists

**PRIMARY OBJECTIVE:**
Create the foundational project structure and development environment.

**IMPLEMENTATION REQUIREMENTS:**

1. **Project Setup:**
   - Create React + TypeScript project using Vite
   - Configure TypeScript with strict mode enabled
   - Set up all necessary configuration files

2. **Dependencies Installation:**
   ```json
   Required packages:
   - react, react-dom, typescript
   - vite, @vitejs/plugin-react
   - tailwindcss, autoprefixer, postcss
   - framer-motion (for animations)
   - zustand (state management)
   - axios (API calls)
   - @types/node, @types/react, @types/react-dom
   - eslint, prettier (code quality)
   - mirascope (LLM functions)
   ```

3. **Folder Structure (CRITICAL - Future phases depend on this):**
   ```
   src/
   ├── components/           # React UI components
   │   ├── ui/              # Reusable UI components
   │   ├── dashboard/       # Dashboard-specific components
   │   └── layout/          # Layout components
   ├── services/            # API and external service integrations
   │   ├── llm/            # OpenRouter + Mirascope integration
   │   ├── database/       # Database operations
   │   └── search/         # Langsearch integration
   ├── stores/             # Zustand store definitions
   ├── types/              # TypeScript type definitions
   ├── utils/              # Utility functions and helpers
   ├── hooks/              # Custom React hooks
   └── constants/          # Application constants
   
   research/               # Research documentation (already exists)
   docs/                   # Implementation documentation
   ```

4. **Configuration Files:**
   - tailwind.config.js (configured for dashboard UI)
   - tsconfig.json (strict mode, path aliases)
   - vite.config.ts (development server, build optimization)
   - .eslintrc.js and .prettierrc (code standards)

5. **Environment Setup:**
   - Create .env.example with required API keys structure
   - Set up environment variable types in src/types/env.d.ts

**COMPLETION REQUIREMENTS:**
1. Project builds without errors
2. Development server starts successfully
3. All configurations are properly set
4. Folder structure matches specification exactly

**DELIVERABLE:**
Create `/docs/phase-01-completion.md` documenting:
- All packages installed and versions
- Folder structure created
- Configuration files added
- Next phase dependencies (what files phase 2 will need)
- Any deviations from the plan and reasons
```

### Research Prompt 1.2 - Database Schema Research
```
Using context7 MCP and fetch tool, research:
1. Best practices for task management database schemas
2. Calendar/reminder database schema patterns
3. Search history and caching strategies
4. PostgreSQL vs SQLite for local development
5. Prisma ORM best practices and schema definitions
6. Database indexing strategies for semantic search
7. Embedding storage patterns if using vector search
8. Data relationships between tasks, events, and search history

Save to /research/02-database-schema.md with:
- Complete schema designs
- Prisma schema examples
- Migration strategies
- Performance optimization tips
```

### Main Prompt 1.2 - Database Setup
```
**CONTEXT: AI Dashboard App - Phase 1.2 Database Setup**
Building on the project foundation from Phase 1.1, you're now implementing the database layer for the AI dashboard.

**PREREQUISITES:**
1. MUST read /research/02-database-schema.md completely
2. MUST read /docs/phase-01-completion.md to understand current project state
3. Verify the following files exist from Phase 1.1:
   - src/services/ directory
   - src/types/ directory
   - package.json with required dependencies
   - Basic project structure

**APP CONTEXT REMINDER:**
The database will store:
- Tasks (from "Remember to do X" inputs)
- Events/Reminders (from "Do X on date Y" inputs)  
- Search History (all user queries and results)
- AI Interactions (for learning and improvement)

**PRIMARY OBJECTIVE:**
Implement complete database infrastructure with Prisma ORM and PostgreSQL.

**IMPLEMENTATION REQUIREMENTS:**

1. **Prisma Setup:**
   - Install: `prisma`, `@prisma/client`, `pg`, `@types/pg`
   - Initialize Prisma in the project
   - Configure database connection for development

2. **Database Schema (src/prisma/schema.prisma):**
   ```prisma
   // Based on research, implement models for:
   model Task {
     id          String   @id @default(cuid())
     title       String
     description String?
     completed   Boolean  @default(false)
     order       Int      @default(0)
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
     // Add fields based on research findings
   }

   model Event {
     id          String   @id @default(cuid())
     title       String
     description String?
     date        DateTime
     completed   Boolean  @default(false)
     createdAt   DateTime @default(now())
     updatedAt   DateTime
     // Add fields based on research findings
   }

   model SearchHistory {
     id        String   @id @default(cuid())
     query     String
     type      String   // 'database' | 'web'
     results   Json?
     createdAt DateTime @default(now())
     // Add fields based on research findings
   }

   model AIInteraction {
     id         String   @id @default(cuid())
     input      String
     category   String   // 'task' | 'event' | 'question'
     response   String
     success    Boolean  @default(true)
     createdAt  DateTime @default(now())
     // Add fields based on research findings
   }
   ```

3. **Database Service Layer (src/services/database/):**
   - `index.ts`: Prisma client initialization
   - `tasks.ts`: All task CRUD operations
   - `events.ts`: All event CRUD operations  
   - `search.ts`: Search history operations
   - `interactions.ts`: AI interaction logging

4. **TypeScript Types (src/types/database.ts):**
   - Export all Prisma types
   - Create custom types for API responses
   - Define database operation interfaces

5. **Development Setup:**
   - Database migration scripts
   - Seed data for testing (sample tasks, events)
   - Connection testing utilities

**CRITICAL INTEGRATION POINTS:**
- Ensure database services export functions that Phase 4+ will use
- Design schema to support real-time updates (Phase 8)
- Plan for Langsearch integration (Phase 6-7)

**COMPLETION REQUIREMENTS:**
1. Database migrations run successfully
2. All CRUD operations tested and working
3. Prisma Client generates types correctly
4. Seed data populates successfully

**DELIVERABLE:**
Update `/docs/phase-01-completion.md` with database section:
- Database schema decisions
- Service layer architecture
- Available database operations for future phases
- Any schema considerations for search integration
```

---

## Phase 2: Core UI Layout

### Research Prompt 2.1 - Dashboard UI Research
```
Using context7 MCP and fetch tool, research:
1. Modern dashboard design patterns in 2025
2. Input bar animation techniques and best practices
3. Response streaming UI patterns (like ChatGPT)
4. Task list UI/UX best practices
5. Calendar component libraries vs custom implementation
6. Search history UI patterns
7. Responsive design strategies for dashboards
8. Accessibility best practices for interactive dashboards
9. Dark mode implementation strategies
10. Micro-interactions and animation timing

Save to /research/03-dashboard-ui.md with:
- Design system recommendations
- Code examples for each component
- Animation timing guidelines
- Accessibility checklists
```

### Main Prompt 2.1 - Dashboard Layout Implementation
```
**CONTEXT: AI Dashboard App - Phase 2.1 Core UI Layout**
You're now building the main dashboard interface. This dashboard has a single input bar where users type commands, a response area showing AI processing, and three main sections: task list, calendar, and recent searches.

**PREREQUISITES:**
1. MUST read /research/03-dashboard-ui.md completely
2. MUST read /docs/phase-01-completion.md to understand project structure
3. Verify these files exist from Phase 1:
   - src/components/ directory structure
   - src/stores/ directory
   - Tailwind CSS configured
   - Framer Motion installed

**APP BEHAVIOR CONTEXT:**
- Input bar will morph to show "Categorizing...", "Task Identified", etc.
- Response area will stream AI responses with typewriter effect
- Task list updates in real-time when tasks are added/completed
- Calendar highlights dates with events
- Recent searches shows last 10 queries with type indicators

**PRIMARY OBJECTIVE:**
Create the main dashboard layout with all core UI components.

**FILES TO CREATE:**

1. **Main Layout Components:**
   - `src/components/layout/DashboardLayout.tsx`: Main container
   - `src/components/dashboard/InputBar.tsx`: Primary input with animations
   - `src/components/dashboard/ResponseArea.tsx`: AI response display
   - `src/components/dashboard/TaskList.tsx`: Task management component
   - `src/components/dashboard/Calendar.tsx`: Calendar display (basic structure)
   - `src/components/dashboard/RecentSearches.tsx`: Search history display

2. **UI Components (src/components/ui/):**
   - `Button.tsx`: Reusable button component
   - `Input.tsx`: Enhanced input with animations
   - `Card.tsx`: Container for sections
   - `Spinner.tsx`: Loading indicators

3. **Zustand Stores (src/stores/):**
   - `dashboardStore.ts`: UI state management
   - `taskStore.ts`: Task list state
   - `searchStore.ts`: Search history state

**IMPLEMENTATION REQUIREMENTS:**

1. **DashboardLayout.tsx:**
   ```tsx
   // Main container with responsive grid
   // Header with app title and theme toggle
   // Grid areas: input, response, tasks, calendar, searches
   // Dark/light mode support with CSS variables
   ```

2. **InputBar.tsx:**
   ```tsx
   // Large, prominent input field
   // Submit button with loading states
   // Morphing animations for different states:
   //   - Default: "Type your command..."
   //   - Processing: "Categorizing..." with pulse
   //   - Category shown: "Task Identified" etc.
   // Focus animations and transitions
   ```

3. **ResponseArea.tsx:**
   ```tsx
   // Scrollable response container
   // Support for streaming text (prepare for typewriter)
   // Progress indicators for AI steps
   // Clear/reset functionality
   // Loading states with animated indicators
   ```

4. **TaskList.tsx:**
   ```tsx
   // Header with sort options (by date, alphabetical, completion)
   // Empty state when no tasks
   // Checkbox interactions for completion
   // Smooth animations for adding/removing items
   // Drag handles for reordering (prepare for Phase 4)
   ```

5. **Calendar.tsx (Basic Structure):**
   ```tsx
   // Month view grid layout
   // Today highlighting
   // Event indicators (dots) on dates
   // Navigation between months
   // Click handling for date selection
   ```

6. **RecentSearches.tsx:**
   ```tsx
   // List of last 10 searches
   // Search type indicators (database/web icons)
   // Click to re-run search functionality
   // Clear history option
   ```

**STYLING REQUIREMENTS:**
- Use Tailwind CSS exclusively
- Implement CSS variables for theming
- Ensure responsive design (mobile-first)
- Add focus states for accessibility
- Use consistent spacing and typography

**STATE MANAGEMENT:**
```tsx
// dashboardStore.ts structure:
interface DashboardState {
  currentInput: string;
  inputState: 'idle' | 'processing' | 'categorized';
  categoryResult: string | null;
  isProcessing: boolean;
  theme: 'light' | 'dark';
}

// taskStore.ts structure:
interface TaskState {
  tasks: Task[];
  sortBy: 'date' | 'alphabetical' | 'completion';
  isLoading: boolean;
}
```

**INTEGRATION POINTS FOR FUTURE PHASES:**
- InputBar must expose methods for morphing animations (Phase 3)
- ResponseArea must support streaming text (Phase 3)
- TaskList must integrate with database operations (Phase 4)
- Calendar must support event display (Phase 5)
- All components must support real-time updates (Phase 8)

**COMPLETION REQUIREMENTS:**
1. All components render without errors
2. Basic interactions work (input, sorting, theme toggle)
3. Responsive design functions on all screen sizes
4. Dark/light mode switches properly
5. Components are properly typed with TypeScript

**DELIVERABLE:**
Create `/docs/phase-02-completion.md` documenting:
- All components created and their responsibilities
- State management architecture
- Styling system used
- Props interfaces for all components
- Integration points prepared for future phases
- Screenshots or descriptions of UI layout
```

### Research Prompt 2.2 - Animation System Research
```
Using context7 MCP and fetch tool, research:
1. Framer Motion advanced patterns for morphing animations
2. Text streaming and typewriter effect implementations
3. Stagger animations for list items
4. Loading and progress animations
5. Smooth transitions between UI states
6. Performance optimization for animations
7. GPU acceleration techniques
8. Animation orchestration patterns

Save to /research/04-animation-system.md with:
- Complete animation code examples
- Performance benchmarks
- Best practices for smooth 60fps animations
```

### Main Prompt 2.2 - Animation System Implementation
```
**CONTEXT: AI Dashboard App - Phase 2.2 Animation System**
Building on the UI layout from Phase 2.1, you're now implementing the comprehensive animation system that makes the dashboard feel alive and responsive.

**PREREQUISITES:**
1. MUST read /research/04-animation-system.md completely
2. MUST read /docs/phase-02-completion.md to understand current UI structure
3. Verify these components exist from Phase 2.1:
   - InputBar.tsx, ResponseArea.tsx, TaskList.tsx
   - Calendar.tsx, RecentSearches.tsx
   - Zustand stores with state management

**APP ANIMATION CONTEXT:**
The dashboard needs specific animations for AI feedback:
- Input bar morphs: idle → "Categorizing..." → "Task Identified"
- Response area: typewriter effect for streaming AI responses
- Task list: smooth additions/removals, completion animations
- Calendar: date highlighting, event appearance
- Overall: loading states, transitions, micro-interactions

**PRIMARY OBJECTIVE:**
Implement comprehensive animation system using Framer Motion.

**FILES TO CREATE/MODIFY:**

1. **Animation System (src/utils/animations/):**
   - `variants.ts`: Framer Motion animation variants
   - `typewriter.ts`: Typewriter effect utilities
   - `morphing.ts`: Input bar morphing animations
   - `list.ts`: List animation helpers

2. **Enhanced Components:**
   - Update all Phase 2.1 components with animations
   - Add animation props and controls
   - Implement performance optimizations

**IMPLEMENTATION REQUIREMENTS:**

1. **Animation Variants (src/utils/animations/variants.ts):**
   ```tsx
   export const inputBarVariants = {
     idle: { scale: 1, borderColor: 'var(--border-color)' },
     processing: { 
       scale: 1.02, 
       borderColor: 'var(--primary-color)',
       boxShadow: '0 0 20px var(--primary-glow)'
     },
     categorized: {
       scale: 1,
       backgroundColor: 'var(--success-bg)',
       transition: { duration: 0.5 }
     }
   };

   export const taskItemVariants = {
     hidden: { opacity: 0, x: -20, scale: 0.95 },
     visible: { opacity: 1, x: 0, scale: 1 },
     exit: { opacity: 0, x: 20, scale: 0.95 }
   };

   export const responseVariants = {
     // Define streaming and loading animations
   };
   ```

2. **Typewriter Effect (src/utils/animations/typewriter.ts):**
   ```tsx
   export const useTypewriter = (text: string, speed: number = 50) => {
     // Implement character-by-character streaming
     // Support for pausing at punctuation
     // Cancellation support for new responses
   };
   ```

3. **Enhanced InputBar.tsx:**
   ```tsx
   // Add Framer Motion integration
   // Implement morphing between states:
   //   - Default state with subtle hover effects
   //   - Processing state with pulsing animation
   //   - Category display with color transition
   //   - Error state with shake animation
   // Support for text morphing in placeholder
   ```

4. **Enhanced ResponseArea.tsx:**
   ```tsx
   // Implement typewriter streaming display
   // Add "AI is thinking" animations
   // Progress indicators for different AI steps:
   //   - "Categorizing..." with dots animation
   //   - "Using task tool..." with tool icon
   //   - "Updating database..." with checkmark
   // Smooth scrolling for long responses
   ```

5. **Enhanced TaskList.tsx:**
   ```tsx
   // Stagger animations for task list items
   // Smooth completion toggle animations
   // Add/remove animations with proper cleanup
   // Drag preview animations (prepare for Phase 4)
   // Sort change animations
   ```

6. **Enhanced Calendar.tsx:**
   ```tsx
   // Month transition animations
   // Date hover effects
   // Event dot appearance animations
   // Today highlighting with gentle pulse
   ```

7. **Performance Optimizations:**
   - Use `layoutId` for shared element transitions
   - Implement `will-change` CSS property correctly
   - Use `transform` and `opacity` for 60fps animations
   - Add reduced motion support

**ANIMATION TIMING STANDARDS:**
- Micro-interactions: 150-200ms
- State transitions: 300-500ms
- Page transitions: 500-800ms
- Loading states: Infinite with 2s cycle
- Typewriter: 30-50ms per character

**STATE INTEGRATION:**
Update Zustand stores to support animation states:
```tsx
interface DashboardState {
  // Previous fields...
  animationState: 'idle' | 'morphing' | 'streaming';
  currentStep: string; // "Categorizing...", "Using tool...", etc.
}
```

**COMPLETION REQUIREMENTS:**
1. All animations run at 60fps on modern devices
2. Reduced motion preferences are respected
3. Animations enhance UX without being distracting
4. Loading states provide clear feedback
5. Typewriter effect works smoothly

**DELIVERABLE:**
Update `/docs/phase-02-completion.md` with animation section:
- Animation system architecture
- Performance optimization techniques used
- Animation timing specifications
- Components enhanced with animations
- Future animation hooks for AI integration (Phase 3)
```

---

## Phase 3: LLM Integration

### Research Prompt 3.1 - OpenRouter and Mirascope Research
```
Using context7 MCP and fetch tool, research extensively:
1. OpenRouter API complete documentation
2. Free tier models available and their capabilities
3. Rate limiting and best practices
4. Mirascope library complete documentation
5. Tool calling patterns without native support
6. Prompt engineering for categorization tasks
7. Streaming response implementation
8. Error handling for LLM APIs
9. Token counting and optimization
10. Fallback strategies for API failures

Save to /research/05-llm-integration.md with:
- Complete API examples
- Mirascope code patterns
- Tool calling workaround implementations
- Error handling strategies
```

### Main Prompt 3.1 - Basic LLM Integration
```
**CONTEXT: AI Dashboard App - Phase 3.1 LLM Integration**
You're now integrating the AI brain of the dashboard. This system will categorize user inputs as TASKS, CALENDAR EVENTS, or QUESTIONS, then respond naturally.

**PREREQUISITES:**
1. MUST read /research/05-llm-integration.md completely
2. MUST read /docs/phase-02-completion.md for UI component interfaces
3. Verify these exist from previous phases:
   - Animation system with morphing capabilities
   - ResponseArea with typewriter support
   - State management for AI interactions
   - Database service layer

**APP AI CONTEXT:**
The LLM will receive inputs like:
- "Remember to do biology homework" → Should categorize as TASK
- "Submit math assignment on 5 August" → Should categorize as CALENDAR EVENT
- "How many tasks do I have?" → Should categorize as QUESTION
No hardcoded keywords - pure AI classification is required.

**PRIMARY OBJECTIVE:**
Implement OpenRouter + Mirascope integration with categorization capabilities.

**FILES TO CREATE:**

1. **LLM Service Layer (src/services/llm/):**
   - `openrouter.ts`: OpenRouter API client
   - `mirascope.ts`: Mirascope integration
   - `categorizer.ts`: Input categorization logic
   - `responder.ts`: Natural language response generation
   - `types.ts`: LLM service interfaces

2. **Configuration:**
   - Update `.env.example` with OpenRouter API key
   - `src/config/llm.ts`: Model selection and parameters

**IMPLEMENTATION REQUIREMENTS:**

1. **OpenRouter Client (src/services/llm/openrouter.ts):**
   ```tsx
   export class OpenRouterClient {
     constructor(apiKey: string) {}
     
     async createCompletion({
       model: string,
       messages: ChatMessage[],
       stream?: boolean,
       max_tokens?: number
     }): Promise<CompletionResponse> {
       // Implement with proper error handling
       // Rate limiting protection
       // Token counting
     }
     
     async streamCompletion(params: CompletionParams): AsyncIterator<string> {
       // Streaming implementation for typewriter effect
     }
   }
   ```

2. **Mirascope Integration (src/services/llm/mirascope.ts):**
   ```tsx
   // Follow research findings for Mirascope patterns
   // Configure with OpenRouter as provider
   // Implement function calling preparation
   ```

3. **Categorization Service (src/services/llm/categorizer.ts):**
   ```tsx
   export interface CategoryResult {
     category: 'TASK' | 'CALENDAR_EVENT' | 'QUESTION';
     confidence: number;
     reasoning: string;
   }
   
   export async function categorizeInput(input: string): Promise<CategoryResult> {
     // Implement prompt-based categorization
     // Use few-shot examples for accuracy
     // Handle edge cases and ambiguous inputs
   }
   ```

4. **Prompt Templates (src/services/llm/prompts.ts):**
   ```tsx
   export const CATEGORIZATION_PROMPT = `
   You are an AI assistant that categorizes user inputs for a personal dashboard.
   
   Categories:
   - TASK: Simple todos without specific dates ("remember to do X", "I need to X")
   - CALENDAR_EVENT: Items with specific dates/times ("do X on Y date", "meeting at Z")
   - QUESTION: Queries about existing data or general questions
   
   Examples:
   Input: "Remember to buy groceries"
   Category: TASK
   
   Input: "Doctor appointment on Friday at 3pm" 
   Category: CALENDAR_EVENT
   
   Input: "How many tasks do I have left?"
   Category: QUESTION
   
   Analyze this input and respond with JSON: {"category": "...", "confidence": 0.95, "reasoning": "..."}
   Input: {input}
   `;
   ```

5. **Integration with UI (update existing files):**
   - Modify `InputBar.tsx` to trigger categorization
   - Update `ResponseArea.tsx` to show AI processing steps
   - Connect animation states to AI progress

6. **State Management Updates:**
   ```tsx
   // Update dashboardStore.ts
   interface DashboardState {
     // Previous fields...
     currentCategory: CategoryResult | null;
     aiProcessing: boolean;
     aiStep: string; // "Categorizing...", "Processing...", etc.
     lastResponse: string;
   }
   
   // Add actions for AI interaction
   const actions = {
     setProcessing: (step: string) => {},
     setCategoryResult: (result: CategoryResult) => {},
     setResponse: (response: string) => {},
   };
   ```

**CRITICAL INTEGRATION WITH ANIMATIONS:**
- When categorization starts: trigger input bar morphing to "Categorizing..."
- When category determined: show "Task Identified" / "Reminder Identified" / "Question Identified"
- During processing: show relevant step animations in ResponseArea
- When complete: stream natural language response with typewriter

**ERROR HANDLING REQUIREMENTS:**
- Network failures with retry logic
- Invalid API responses with graceful fallbacks
- Rate limit handling with user feedback
- Categorization uncertainty handling

**COMPLETION REQUIREMENTS:**
1. Successfully categorizes all three input types
2. Streaming responses work with typewriter animation
3. Error states display user-friendly messages
4. Rate limiting prevents API abuse
5. Integration with UI animations is smooth

**DELIVERABLE:**
Create `/docs/phase-03-completion.md` documenting:
- LLM service architecture
- Categorization accuracy and edge cases tested
- API integration details and rate limits
- Error handling strategies implemented
- UI integration points completed
- Next phase requirements (tool calling setup)
```

### Research Prompt 3.2 - Tool Calling Workaround Research
```
Using context7 MCP and fetch tool, research:
1. JSON schema validation for tool outputs
2. Prompt engineering for reliable JSON generation
3. Parsing strategies for LLM-generated tool calls
4. Error recovery when JSON parsing fails
5. Best practices for tool descriptions in prompts
6. Multiple tool calling patterns
7. Validation and sanitization techniques
8. Examples from similar implementations

Save to /research/06-tool-calling-workaround.md with:
- Complete implementation patterns
- Prompt templates
- Parsing algorithms
- Error handling strategies
```

### Main Prompt 3.2 - Tool Calling Implementation
```
**CONTEXT: AI Dashboard App - Phase 3.2 Tool Calling System**
Building on LLM integration from Phase 3.1, you're implementing the tool calling workaround that enables the AI to execute actions like creating tasks, events, and searches.

**PREREQUISITES:**
1. MUST read /research/06-tool-calling-workaround.md completely
2. MUST read /docs/phase-03-completion.md to understand LLM integration
3. Verify these exist from Phase 3.1:
   - Working categorization system
   - OpenRouter + Mirascope integration
   - Animation integration with AI steps
   - Database service layer from Phase 1.2

**TOOL CALLING CONTEXT:**
After categorization, the AI needs to:
- TASK: Use CreateTaskTool to extract task name and store in database
- CALENDAR_EVENT: Use CreateEventTool to extract event details and date
- QUESTION: Use DatabaseSearchTool or WebSearchTool based on query type

**PRIMARY OBJECTIVE:**
Implement robust tool calling workaround with JSON parsing and execution.

**FILES TO CREATE:**

1. **Tool System (src/services/llm/tools/):**
   - `types.ts`: Tool interfaces and schemas
   - `registry.ts`: Tool registration and management
   - `parser.ts`: JSON extraction and validation
   - `executor.ts`: Tool execution orchestration

2. **Individual Tools:**
   - `CreateTaskTool.ts`: Task creation from natural language
   - `CreateEventTool.ts`: Event creation with date parsing
   - `DatabaseSearchTool.ts`: Semantic search preparation
   - `WebSearchTool.ts`: Web search preparation

**IMPLEMENTATION REQUIREMENTS:**

1. **Tool Interface (src/services/llm/tools/types.ts):**
   ```tsx
   interface Tool {
     name: string;
     description: string;
     parameters: {
       type: 'object';
       properties: Record<string, any>;
       required: string[];
     };
     execute: (params: any) => Promise<ToolResult>;
   }
   
   interface ToolResult {
     success: boolean;
     data?: any;
     error?: string;
     message?: string; // For user feedback
   }
   
   interface ToolCall {
     tool: string;
     parameters: Record<string, any>;
   }
   ```

2. **CreateTaskTool (src/services/llm/tools/CreateTaskTool.ts):**
   ```tsx
   export const CreateTaskTool: Tool = {
     name: 'create_task',
     description: 'Create a new task from user input',
     parameters: {
       type: 'object',
       properties: {
         title: { type: 'string', description: 'Clear, concise task title' },
         description: { type: 'string', description: 'Optional task details' }
       },
       required: ['title']
     },
     execute: async (params) => {
       // Use database service from Phase 1.2
       // Create task in database
       // Return success/failure result
     }
   };
   ```

3. **Tool Parser (src/services/llm/tools/parser.ts):**
   ```tsx
   export class ToolParser {
     extractJSON(llmResponse: string): any {
       // Robust JSON extraction from LLM response
       // Handle code blocks, partial JSON, malformed JSON
       // Multiple extraction strategies
     }
     
     validateToolCall(json: any, availableTools: Tool[]): ToolCall {
       // Schema validation
       // Parameter type checking
       // Required field validation
     }
     
     parseToolCalls(response: string): ToolCall[] {
       // Support for multiple tool calls
       // Error recovery mechanisms
     }
   }
   ```

4. **Enhanced Prompts (update src/services/llm/prompts.ts):**
   ```tsx
   export const TOOL_CALLING_PROMPT = `
   You are an AI assistant with access to tools. After categorizing the input, you must call the appropriate tool.
   
   Available tools:
   - create_task: For TASK category inputs
   - create_event: For CALENDAR_EVENT category inputs  
   - database_search: For QUESTION category about user data
   - web_search: For QUESTION category about general information
   
   Always respond with JSON in this format:
   {
     "tool": "tool_name",  
     "parameters": { "param1": "value1", "param2": "value2" }
   }
   
   Input: {input}
   Category: {category}
   
   Tool call:
   `;
   ```

5. **Tool Executor (src/services/llm/tools/executor.ts):**
   ```tsx
   export class ToolExecutor {
     private tools: Map<string, Tool> = new Map();
     
     registerTool(tool: Tool) {
       this.tools.set(tool.name, tool);
     }
     
     async executeToolCall(toolCall: ToolCall): Promise<ToolResult> {
       // Find and execute tool
       // Handle errors gracefully
       // Log execution for debugging
     }
     
     async executeWithFeedback(
       toolCall: ToolCall,
       onProgress: (step: string) => void
     ): Promise<ToolResult> {
       // Execute with UI feedback
       // Update animation states during execution
     }
   }
   ```

6. **Integration Updates:**
   - Update categorization flow to include tool calling
   - Enhance ResponseArea to show tool execution steps
   - Connect tool results to natural language responses

**ANIMATION INTEGRATION:**
- "Using task tool..." when CreateTaskTool executes
- "Updating database..." during database operations
- "Task created!" on successful completion
- Error animations for tool failures

**ERROR HANDLING:**
- JSON parsing failures with retry attempts
- Tool execution errors with user explanations
- Parameter validation with specific error messages
- Fallback responses when tools fail

**COMPLETION REQUIREMENTS:**
1. Tool calling works reliably for all categories
2. JSON parsing handles malformed responses gracefully
3. Database integration works for task/event creation
4. UI animations sync with tool execution steps
5. Error states provide actionable feedback

**DELIVERABLE:**
Update `/docs/phase-03-completion.md` with tool calling section:
- Tool calling architecture and reliability
- JSON parsing strategies and success rates
- Tool execution integration with database
- Animation synchronization with tool steps
- Error handling coverage
- Phase 4 readiness (complete task flow implementation)
```

---

## Phase 4: Task Flow Implementation

### Research Prompt 4.1 - Task Management Research
```
Using context7 MCP and fetch tool, research:
1. Task management best practices
2. Natural language processing for task extraction
3. Task prioritization algorithms
4. Drag-and-drop implementation for task reordering
5. Task completion animations
6. Batch operations on tasks
7. Task search and filtering
8. Undo/redo patterns for task operations

Save to /research/07-task-management.md with:
- Implementation examples
- UX best practices
- Performance considerations
```

### Main Prompt 4.1 - Complete Task Flow
```
**CONTEXT: AI Dashboard App - Phase 4.1 Complete Task Flow**
You're now implementing the full task management flow: user input → AI categorization → tool execution → database update → UI update → natural language response.

**PREREQUISITES:**
1. MUST read /research/07-task-management.md completely
2. MUST read /docs/phase-03-completion.md to understand tool calling system
3. Verify these components work from previous phases:
   - CreateTaskTool with database integration
   - TaskList.tsx with animation support
   - Input categorization and tool parsing
   - Typewriter response streaming

**TASK FLOW CONTEXT:**
Complete flow example:
1. User types: "Remember to do biology homework"
2. InputBar shows: "Categorizing..." → "Task Identified"
3. AI uses CreateTaskTool, ResponseArea shows: "Extracting details..." → "Using task tool..." → "Task list updated!"
4. TaskList updates with new item and animation
5. ResponseArea streams: "I've added 'Biology Homework' to your task list. Anything else?"

**PRIMARY OBJECTIVE:**
Complete the end-to-end task creation and management flow.

**FILES TO ENHANCE/CREATE:**

1. **Enhanced CreateTaskTool (src/services/llm/tools/CreateTaskTool.ts):**
   - Improve natural language extraction
   - Better task title generation
   - Handle edge cases and ambiguous inputs

2. **Task Management (src/services/tasks/):**
   - `taskService.ts`: Enhanced CRUD operations
   - `taskUtils.ts`: Sorting, filtering, validation utilities

3. **Enhanced TaskList Component:**
   - Real-time updates from database
   - Interactive features (complete, delete, edit, reorder)
   - Advanced sorting and filtering

4. **Response Generation:**
   - Natural language templates for task operations
   - Context-aware responses with task details

**IMPLEMENTATION REQUIREMENTS:**

1. **Enhanced CreateTaskTool Implementation:**
   ```tsx
   export const CreateTaskTool: Tool = {
     // Previous definition...
     execute: async (params) => {
       try {
         // Validate and sanitize task title
         const taskTitle = extractAndCleanTitle(params.title);
         
         // Create task in database using Phase 1.2 service
         const task = await taskService.create({
           title: taskTitle,
           description: params.description || null,
           completed: false,
           order: await getNextTaskOrder()
         });
         
         // Trigger real-time update (prepare for Phase 8)
         await broadcastTaskUpdate('created', task);
         
         return {
           success: true,
           data: task,
           message: `Created task: "${task.title}"`
         };
       } catch (error) {
         return {
           success: false,
           error: error.message,
           message: 'Failed to create task. Please try again.'
         };
       }
     }
   };
   ```

2. **Enhanced Task Service (src/services/tasks/taskService.ts):**
   ```tsx
   export class TaskService {
     // Build on Phase 1.2 database service
     
     async create(taskData: CreateTaskInput): Promise<Task> {
       // Create with proper ordering
       // Handle duplicate titles
       // Validate required fields
     }
     
     async update(id: string, updates: UpdateTaskInput): Promise<Task> {
       // Partial updates
       // Optimistic locking
       // Validation
     }
     
     async delete(id: string): Promise<void> {
       // Soft delete option
       // Cascade handling
     }
     
     async reorder(taskIds: string[]): Promise<void> {
       // Batch update for drag-and-drop
       // Atomic operation
     }
     
     async getAll(options?: TaskQueryOptions): Promise<Task[]> {
       // Sorting options
       // Filtering capabilities
       // Pagination ready
     }
   }
   ```

3. **Enhanced TaskList.tsx Component:**
   ```tsx
   export function TaskList() {
     // Connect to taskStore from Phase 2
     // Real-time updates subscription
     
     const handleToggleComplete = async (taskId: string) => {
       // Optimistic UI update
       // Database sync
       // Animation trigger
     };
     
     const handleDelete = async (taskId: string) => {
       // Confirmation dialog
       // Undo functionality
       // Smooth removal animation
     };
     
     const handleReorder = async (dragResult: DropResult) => {
       // Drag-and-drop handling
       // Immediate UI update
       // Background database sync
     };
     
     return (
       <motion.div className="task-list">
         <TaskListHeader 
           sortBy={sortBy}
           onSortChange={handleSortChange}
           taskCount={tasks.length}
         />
         
         <AnimatePresence>
           {tasks.map((task) => (
             <TaskItem
               key={task.id}
               task={task}
               onToggle={handleToggleComplete}
               onDelete={handleDelete}
               onEdit={handleInlineEdit}
             />
           ))}
         </AnimatePresence>
         
         {tasks.length === 0 && <EmptyTaskState />}
       </motion.div>
     );
   }
   ```

4. **TaskItem Component (src/components/dashboard/TaskItem.tsx):**
   ```tsx
   export function TaskItem({ task, onToggle, onDelete, onEdit }: TaskItemProps) {
     return (
       <motion.div
         layout
         variants={taskItemVariants}
         initial="hidden"
         animate="visible"
         exit="exit"
         className="task-item"
       >
         <Checkbox 
           checked={task.completed}
           onChange={() => onToggle(task.id)}
         />
         
         <InlineEditText
           value={task.title}
           onSave={(newTitle) => onEdit(task.id, { title: newTitle })}
         />
         
         <DeleteButton onClick={() => onDelete(task.id)} />
         <DragHandle />
       </motion.div>
     );
   }
   ```

5. **Natural Language Response Generation:**
   ```tsx
   // Enhanced src/services/llm/responder.ts
   export function generateTaskResponse(
     action: 'created' | 'updated' | 'deleted',
     task: Task,
     context?: string
   ): string {
     const templates = {
       created: [
         `I've added "${task.title}" to your task list. Anything else?`,
         `Great! "${task.title}" is now in your tasks. What would you like to do next?`,
         `Task "${task.title}" has been created successfully. Need help with anything else?`
       ],
       // More templates for variety...
     };
     
     return selectRandomTemplate(templates[action]);
   }
   ```

6. **Enhanced State Management:**
   ```tsx
   // Update src/stores/taskStore.ts
   interface TaskState {
     tasks: Task[];
     sortBy: 'date' | 'alphabetical' | 'completion' | 'custom';
     filterBy: 'all' | 'active' | 'completed';
     isLoading: boolean;
     lastUpdate: Date | null;
   }
   
   const actions = {
     addTask: (task: Task) => {
       // Optimistic update with animation trigger
     },
     updateTask: (id: string, updates: Partial<Task>) => {
       // Immediate UI update
     },
     deleteTask: (id: string) => {
       // Removal with undo option
     },
     reorderTasks: (newOrder: string[]) => {
       // Drag-and-drop result handling
     }
   };
   ```

**INTEGRATION REQUIREMENTS:**
- Connect tool execution results with UI updates
- Ensure animations play during each step of the flow
- Handle error states gracefully with user feedback
- Implement optimistic updates for better UX

**REAL-TIME CONSIDERATIONS:**
- Prepare for WebSocket integration (Phase 8)
- Design for multi-tab synchronization
- Handle concurrent updates gracefully

**COMPLETION REQUIREMENTS:**
1. Complete task flow works from input to response
2. Task list updates in real-time with smooth animations
3. All task management features work (add, edit, delete, reorder, sort)
4. Natural language responses are contextual and varied
5. Error handling covers all failure points

**DELIVERABLE:**
Create `/docs/phase-04-completion.md` documenting:
- Complete task flow implementation and testing
- Task management features implemented
- UI/Animation integration success
- Natural language response quality
- Error handling coverage
- Database operations performance
- Ready for Phase 5 (calendar flow implementation)
```

### Research Prompt 4.2 - Natural Language Response Research
```
Using context7 MCP and fetch tool, research:
1. Natural language generation best practices
2. Context-aware response generation
3. Personality and tone consistency
4. Response variation to avoid repetition
5. Multilingual support considerations
6. Response length optimization
7. Conversational flow patterns

Save to /research/08-natural-language-response.md
```

### Main Prompt 4.2 - Natural Language Response Enhancement
```
**CONTEXT: AI Dashboard App - Phase 4.2 Response Enhancement**
Building on the task flow from Phase 4.1, you're enhancing the natural language response system to be more conversational, contextual, and engaging.

**PREREQUISITES:**
1. MUST read /research/08-natural-language-response.md completely
2. MUST read /docs/phase-04-completion.md to understand current task flow
3. Verify these work from Phase 4.1:
   - Complete task creation flow
   - Tool execution with database updates
   - Basic response generation
   - Streaming typewriter animation

**RESPONSE CONTEXT:**
The AI should feel conversational and helpful:
- Reference specific task details in responses
- Maintain conversation flow and context
- Vary responses to avoid repetition
- Handle follow-up questions naturally
- Provide helpful suggestions when appropriate

**PRIMARY OBJECTIVE:**
Create an intelligent, context-aware response generation system.

**FILES TO CREATE/ENHANCE:**

1. **Response System (src/services/llm/responses/):**
   - `responseGenerator.ts`: Main response orchestration
   - `templates.ts`: Response template library
   - `contextManager.ts`: Conversation context tracking
   - `personalization.ts`: User-specific response adaptation

2. **Enhanced Streaming:**
   - Improved typewriter effect with natural pauses
   - Response cancellation and replacement
   - Chunk-based streaming for better performance

**IMPLEMENTATION REQUIREMENTS:**

1. **Response Generator (src/services/llm/responses/responseGenerator.ts):**
   ```tsx
   export class ResponseGenerator {
     private context: ConversationContext;
     private templates: ResponseTemplates;
     
     async generateResponse(
       action: ActionType,
       result: ToolResult,
       userInput: string,
       context?: ConversationContext
     ): Promise<GeneratedResponse> {
       
       // Analyze action success/failure
       if (!result.success) {
         return this.generateErrorResponse(action, result.error, userInput);
       }
       
       // Generate contextual success response
       const response = await this.generateSuccessResponse(action, result, context);
       
       // Add helpful follow-up suggestions
       const suggestions = this.generateFollowUpSuggestions(action, result);
       
       return {
         text: response,
         suggestions,
         metadata: { action, timestamp: new Date(), context }
       };
     }
     
     private async generateSuccessResponse(
       action: ActionType,
       result: ToolResult,
       context?: ConversationContext
     ): Promise<string> {
       // Context-aware response generation
       // Template selection based on recent history
       // Personalization based on user patterns
     }
   }
   ```

2. **Response Templates (src/services/llm/responses/templates.ts):**
   ```tsx
   export const ResponseTemplates = {
     taskCreated: {
       standard: [
         "I've added '{taskTitle}' to your task list. Anything else?",
         "Great! '{taskTitle}' is now in your tasks. What would you like to do next?",
         "Task '{taskTitle}' has been created successfully. Need help with anything else?"
       ],
       contextual: {
         firstTask: "Welcome! I've created your first task: '{taskTitle}'. You can add more tasks, set reminders, or ask me questions about your data.",
         multipleToday: "That's task number {todayCount} for today! '{taskTitle}' is now added to your list.",
         similarTask: "I notice you have similar tasks. '{taskTitle}' has been added to your list. Would you like me to group related tasks?"
       }
     },
     
     taskError: {
       duplicateTitle: "It looks like you already have a task called '{taskTitle}'. Would you like me to create it anyway or modify the existing one?",
       invalidInput: "I couldn't extract a clear task from '{userInput}'. Could you rephrase it? For example: 'Remember to call mom' or 'Buy groceries'.",
       databaseError: "I had trouble saving your task to the database. Please try again in a moment."
     }
   };
   ```

3. **Context Manager (src/services/llm/responses/contextManager.ts):**
   ```tsx
   interface ConversationContext {
     recentActions: ActionHistory[];
     userPatterns: UserPatterns;
     sessionStats: SessionStats;
     preferences: UserPreferences;
   }
   
   export class ContextManager {
     private context: ConversationContext;
     
     updateContext(action: ActionType, result: ToolResult, userInput: string) {
       // Track user interaction patterns
       // Update session statistics
       // Learn from user preferences
     }
     
     getContextualHints(action: ActionType): ContextualHints {
       // Provide context for response generation
       // Suggest personalization strategies
       // Identify conversation patterns
     }
     
     shouldSuggestFollowUp(): boolean {
       // Determine if follow-up suggestions are appropriate
       // Based on user engagement patterns
     }
   }
   ```

4. **Enhanced Streaming (src/components/dashboard/StreamingResponse.tsx):**
   ```tsx
   export function StreamingResponse({ response, onComplete }: StreamingResponseProps) {
     const [displayedText, setDisplayedText] = useState('');
     const [isStreaming, setIsStreaming] = useState(false);
     
     const streamText = useCallback(async (text: string) => {
       setIsStreaming(true);
       setDisplayedText('');
       
       for (let i = 0; i <= text.length; i++) {
         // Natural pauses at punctuation
         const char = text[i-1];
         let delay = 30; // base delay
         
         if (char === '.' || char === '!' || char === '?') delay = 150;
         else if (char === ',' || char === ';') delay = 75;
         
         await new Promise(resolve => setTimeout(resolve, delay));
         setDisplayedText(text.slice(0, i));
       }
       
       setIsStreaming(false);
       onComplete?.();
     }, [onComplete]);
     
     return (
       <motion.div className="streaming-response">
         {displayedText}
         {isStreaming && <TypingCursor />}
       </motion.div>
     );
   }
   ```

5. **Response Integration Updates:**
   - Update tool execution to use enhanced response generation
   - Connect context manager to conversation flow
   - Integrate suggestions with UI
   - Add response history tracking

**CONVERSATION FLOW ENHANCEMENTS:**
- Track conversation state across interactions
- Remember user preferences and patterns
- Provide contextual help and suggestions
- Handle follow-up questions intelligently

**PERSONALIZATION FEATURES:**
- Learn from user interaction patterns
- Adapt response tone and length
- Remember preferred task formats
- Suggest improvements based on usage

**COMPLETION REQUIREMENTS:**
1. Responses feel natural and conversational
2. Context is maintained across interactions
3. Response variations prevent repetition
4. Error messages are helpful and specific
5. Streaming animations enhance the experience

**DELIVERABLE:**
Update `/docs/phase-04-completion.md` with response enhancement section:
- Response generation architecture
- Context management implementation
- Template variety and personalization
- Streaming enhancement details
- Conversation flow improvements
- Phase 5 readiness (calendar flow with enhanced responses)
```

---

## Phase 5: Calendar/Reminder Flow

### Research Prompt 5.1 - Calendar Implementation Research
```
Using context7 MCP and fetch tool, research:
1. Calendar UI libraries vs custom implementation
2. Date parsing from natural language
3. Timezone handling best practices
4. Recurring event patterns
5. Calendar view options (month, week, day)
6. Event collision detection
7. Reminder notification strategies
8. Calendar data synchronization

Save to /research/09-calendar-implementation.md with:
- Library comparisons
- Date parsing strategies
- Implementation examples
```

### Main Prompt 5.1 - Calendar Component Implementation
```
**CONTEXT: AI Dashboard App - Phase 5.1 Calendar Implementation**
You're now building the calendar functionality that handles date-based inputs like "Submit math assignment on 5 August" and displays events in an interactive calendar.

**PREREQUISITES:**
1. MUST read /research/09-calendar-implementation.md completely
2. MUST read /docs/phase-04-completion.md to understand task flow implementation
3. Verify these exist from previous phases:
   - CreateEventTool foundation (from Phase 3.2)
   - Calendar.tsx basic structure (from Phase 2.1)
   - Database Event model (from Phase 1.2)
   - Enhanced response generation system (from Phase 4.2)

**CALENDAR CONTEXT:**
The calendar will handle inputs like:
- "Doctor appointment on Friday at 3pm"
- "Submit assignment on August 5th"
- "Meeting tomorrow at 2pm"
- "Vacation from July 1st to July 10th"

**PRIMARY OBJECTIVE:**
Implement a fully functional calendar component with natural language date parsing.

**FILES TO CREATE/ENHANCE:**

1. **Date Parsing (src/services/dates/):**
   - `dateParser.ts`: Natural language date extraction
   - `dateUtils.ts`: Date manipulation utilities
   - `timezoneUtils.ts`: Timezone handling

2. **Enhanced CreateEventTool:**
   - Natural language date parsing
   - Time extraction and validation
   - Recurring event support (basic)

3. **Calendar Component Enhancement:**
   - Interactive month/week/day views
   - Event display and management
   - Real-time updates

4. **Event Management:**
   - CRUD operations for events
   - Conflict detection
   - Event categorization

**IMPLEMENTATION REQUIREMENTS:**

1. **Date Parser (src/services/dates/dateParser.ts):**
   ```tsx
   export class DateParser {
     parseNaturalDate(input: string, referenceDate?: Date): ParsedDate {
       // Handle various date formats:
       // - "August 5th", "Aug 5", "8/5/2025"
       // - "tomorrow", "next Friday", "in 3 days"
       // - "this weekend", "next month"
       // - Time: "at 3pm", "at 15:30", "in the morning"
       
       const patterns = {
         absolute: /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/,
         monthDay: /(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})/i,
         relative: /(tomorrow|today|yesterday|next\s+\w+|this\s+\w+|in\s+\d+\s+days?)/i,
         time: /(?:at\s+)?(\d{1,2})(?::(\d{2}))?\s*(am|pm|AM|PM)/
       };
       
       return {
         date: extractedDate,
         time: extractedTime,
         isAllDay: !extractedTime,
         confidence: confidenceScore,
         originalText: input
       };
     }
     
     validateDate(parsedDate: ParsedDate): ValidationResult {
       // Check if date is in the past (with warnings)
       // Validate time format
       // Check for conflicts
     }
   }
   ```

2. **Enhanced CreateEventTool (src/services/llm/tools/CreateEventTool.ts):**
   ```tsx
   export const CreateEventTool: Tool = {
     name: 'create_event',
     description: 'Create a calendar event or reminder from user input',
     parameters: {
       type: 'object',
       properties: {
         title: { type: 'string', description: 'Event title' },
         description: { type: 'string', description: 'Optional event details' },
         dateText: { type: 'string', description: 'Date/time text from user input' },
         isReminder: { type: 'boolean', description: 'True if this is a reminder vs appointment' }
       },
       required: ['title', 'dateText']
     },
     
     execute: async (params) => {
       try {
         // Parse natural language date
         const dateParser = new DateParser();
         const parsedDate = dateParser.parseNaturalDate(params.dateText);
         
         if (parsedDate.confidence < 0.7) {
           return {
             success: false,
             error: 'date_unclear',
             message: `I'm not sure about the date "${params.dateText}". Could you clarify?`
           };
         }
         
         // Create event in database
         const event = await eventService.create({
           title: params.title,
           description: params.description,
           date: parsedDate.date,
           isAllDay: parsedDate.isAllDay,
           isReminder: params.isReminder || false
         });
         
         // Trigger calendar update
         await broadcastEventUpdate('created', event);
         
         return {
           success: true,
           data: event,
           message: `Created ${params.isReminder ? 'reminder' : 'event'}: "${event.title}" for ${formatDate(event.date)}`
         };
         
       } catch (error) {
         return {
           success: false,
           error: error.message,
           message: 'Failed to create event. Please try again.'
         };
       }
     }
   };
   ```

3. **Enhanced Calendar Component (src/components/dashboard/Calendar.tsx):**
   ```tsx
   export function Calendar() {
     const [currentDate, setCurrentDate] = useState(new Date());
     const [view, setView] = useState<'month' | 'week' | 'day'>('month');
     const [events, setEvents] = useState<Event[]>([]);
     const [selectedDate, setSelectedDate] = useState<Date | null>(null);
     
     // Real-time event updates
     useEffect(() => {
       // Subscribe to event updates (prepare for Phase 8)
       const subscription = subscribeToEventUpdates((event, action) => {
         if (action === 'created') {
           setEvents(prev => [...prev, event]);
         }
         // Handle other actions...
       });
       
       return () => subscription.unsubscribe();
     }, []);
     
     const renderCalendarGrid = () => {
       const daysInMonth = getDaysInMonth(currentDate);
       const firstDayOfMonth = getFirstDayOfMonth(currentDate);
       
       return (
         <div className="calendar-grid">
           {/* Week headers */}
           {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
             <div key={day} className="calendar-header">{day}</div>
           ))}
           
           {/* Calendar days */}
           {daysInMonth.map(date => (
             <CalendarDay
               key={date.toISOString()}
               date={date}
               events={getEventsForDate(events, date)}
               isToday={isToday(date)}
               isSelected={isSameDay(date, selectedDate)}
               onClick={() => setSelectedDate(date)}
             />
           ))}
         </div>
       );
     };
     
     return (
       <motion.div className="calendar-container">
         <CalendarHeader
           currentDate={currentDate}
           view={view}
           onDateChange={setCurrentDate}
           onViewChange={setView}
         />
         
         <AnimatePresence mode="wait">
           <motion.div
             key={`${currentDate.getMonth()}-${currentDate.getFullYear()}`}
             initial={{ opacity: 0, x: 20 }}
             animate={{ opacity: 1, x: 0 }}
             exit={{ opacity: 0, x: -20 }}
           >
             {renderCalendarGrid()}
           </motion.div>
         </AnimatePresence>
         
         {selectedDate && (
           <DayDetailModal
             date={selectedDate}
             events={getEventsForDate(events, selectedDate)}
             onClose={() => setSelectedDate(null)}
           />
         )}
       </motion.div>
     );
   }
   ```

4. **Calendar Day Component (src/components/dashboard/CalendarDay.tsx):**
   ```tsx
   export function CalendarDay({ date, events, isToday, isSelected, onClick }: CalendarDayProps) {
     return (
       <motion.div
         className={cn(
           'calendar-day',
           isToday && 'today',
           isSelected && 'selected',
           events.length > 0 && 'has-events'
         )}
         onClick={onClick}
         whileHover={{ scale: 1.05 }}
         whileTap={{ scale: 0.95 }}
       >
         <span className="day-number">{date.getDate()}</span>
         
         {events.length > 0 && (
           <div className="event-indicators">
             {events.slice(0, 3).map((event, index) => (
               <motion.div
                 key={event.id}
                 className={cn('event-dot', event.isReminder && 'reminder')}
                 initial={{ scale: 0 }}
                 animate={{ scale: 1 }}
                 transition={{ delay: index * 0.1 }}
               />
             ))}
             {events.length > 3 && (
               <span className="more-events">+{events.length - 3}</span>
             )}
           </div>
         )}
         
         {isToday && (
           <motion.div
             className="today-indicator"
             animate={{ scale: [1, 1.2, 1] }}
             transition={{ repeat: Infinity, duration: 2 }}
           />
         )}
       </motion.div>
     );
   }
   ```

5. **Event Service (src/services/events/eventService.ts):**
   ```tsx
   export class EventService {
     // Build on Phase 1.2 database service
     
     async create(eventData: CreateEventInput): Promise<Event> {
       // Validate date is in future (with warnings for past dates)
       // Check for conflicts
       // Create with proper timezone handling
     }
     
     async getForDateRange(startDate: Date, endDate: Date): Promise<Event[]> {
       // Efficient date range queries
       // Include recurring events
       // Sort by date/time
     }
     
     async getConflicts(event: Event): Promise<Event[]> {
       // Find overlapping events
       // Consider buffer times
       // Return potential conflicts
     }
     
     async update(id: string, updates: UpdateEventInput): Promise<Event> {
       // Handle date changes
       // Validate new timing
       // Update notifications
     }
   }
   ```

**INTEGRATION WITH EXISTING SYSTEMS:**
- Connect with enhanced response generation from Phase 4.2
- Use animation system from Phase 2.2 for smooth transitions
- Integrate with tool calling system from Phase 3.2
- Ensure real-time updates prepare for Phase 8

**DATE PARSING REQUIREMENTS:**
- Handle various date formats and natural language
- Provide confidence scores for ambiguous dates
- Request clarification for unclear dates
- Support timezone awareness

**COMPLETION REQUIREMENTS:**
1. Calendar displays events correctly with smooth animations
2. Date parsing handles natural language inputs accurately
3. CreateEventTool integrates with calendar display
4. Event management (view, edit, delete) works properly
5. Real-time updates are prepared for implementation

**DELIVERABLE:**
Create `/docs/phase-05-completion.md` documenting:
- Calendar component architecture and features
- Date parsing accuracy and supported formats
- Event management implementation
- Integration with AI tool calling system
- UI/UX enhancements and animations
- Phase 5.2 readiness (reminder system implementation)
```

### Research Prompt 5.2 - Reminder System Research
```
Using context7 MCP and fetch tool, research:
1. Browser notification API
2. Background sync for reminders
3. Service worker implementation
4. Reminder scheduling algorithms
5. Notification permission handling
6. Fallback strategies when notifications unavailable
7. Reminder snooze patterns

Save to /research/10-reminder-system.md
```

### Main Prompt 5.2 - Reminder System Implementation
```
**CONTEXT: AI Dashboard App - Phase 5.2 Reminder System**
Building on the calendar from Phase 5.1, you're implementing the reminder notification system that alerts users about upcoming events.

**PREREQUISITES:**
1. MUST read /research/10-reminder-system.md completely
2. MUST read /docs/phase-05-completion.md to understand calendar implementation
3. Verify these work from Phase 5.1:
   - Calendar component with event display
   - CreateEventTool with date parsing
   - Event service with CRUD operations
   - Natural language response integration

**REMINDER CONTEXT:**
The system should:
- Check for upcoming events/reminders
- Show browser notifications (with permission)
- Display in-app reminder alerts
- Allow snoozing and dismissing
- Handle notification permissions gracefully

**PRIMARY OBJECTIVE:**
Implement comprehensive reminder and notification system.

**FILES TO CREATE:**

1. **Notification System (src/services/notifications/):**
   - `notificationService.ts`: Browser notification management
   - `reminderScheduler.ts`: Reminder checking and scheduling
   - `inAppNotifications.ts`: In-app notification display

2. **Service Worker:**
   - `public/sw.js`: Background notification handling
   - Service worker registration and management

3. **Reminder Components:**
   - `ReminderToast.tsx`: In-app reminder display
   - `NotificationPermission.tsx`: Permission request UI

**IMPLEMENTATION REQUIREMENTS:**

1. **Notification Service (src/services/notifications/notificationService.ts):**
   ```tsx
   export class NotificationService {
     private hasPermission = false;
     
     async requestPermission(): Promise<boolean> {
       if (!('Notification' in window)) {
         console.warn('Browser does not support notifications');
         return false;
       }
       
       if (Notification.permission === 'granted') {
         this.hasPermission = true;
         return true;
       }
       
       if (Notification.permission !== 'denied') {
         const permission = await Notification.requestPermission();
         this.hasPermission = permission === 'granted';
         return this.hasPermission;
       }
       
       return false;
     }
     
     async showNotification(event: Event, options?: NotificationOptions): Promise<void> {
       if (!this.hasPermission) {
         // Fall back to in-app notification
         this.showInAppNotification(event);
         return;
       }
       
       const notification = new Notification(event.title, {
         body: event.description || `Reminder: ${event.title}`,
         icon: '/icon-192.png',
         badge: '/badge-72.png',
         tag: `event-${event.id}`,
         requireInteraction: true,
         actions: [
           { action: 'snooze', title: 'Snooze 10min' },
           { action: 'dismiss', title: 'Dismiss' }
         ],
         ...options
       });
       
       notification.onclick = () => {
         window.focus();
         // Navigate to calendar date
         this.focusEvent(event);
       };
     }
     
     private showInAppNotification(event: Event): void {
       // Trigger in-app notification store
       inAppNotificationStore.addNotification({
         id: `reminder-${event.id}`,
         type: 'reminder',
         title: event.title,
         message: event.description || `Time for: ${event.title}`,
         event,
         actions: ['snooze', 'dismiss']
       });
     }
   }
   ```

2. **Reminder Scheduler (src/services/notifications/reminderScheduler.ts):**
   ```tsx
   export class ReminderScheduler {
     private checkInterval: NodeJS.Timeout | null = null;
     private notificationService = new NotificationService();
     
     start(): void {
       // Check for upcoming reminders every minute
       this.checkInterval = setInterval(() => {
         this.checkUpcomingReminders();
       }, 60000);
       
       // Also check immediately
       this.checkUpcomingReminders();
     }
     
     stop(): void {
       if (this.checkInterval) {
         clearInterval(this.checkInterval);
         this.checkInterval = null;
       }
     }
     
     private async checkUpcomingReminders(): Promise<void> {
       const now = new Date();
       const upcoming = new Date(now.getTime() + 15 * 60 * 1000); // 15 minutes from now
       
       try {
         const events = await eventService.getForDateRange(now, upcoming);
         
         for (const event of events) {
           if (this.shouldNotify(event, now)) {
             await this.notificationService.showNotification(event);
             
             // Mark as notified to avoid spam
             await eventService.markNotified(event.id);
           }
         }
       } catch (error) {
         console.error('Error checking reminders:', error);
       }
     }
     
     private shouldNotify(event: Event, now: Date): boolean {
       const eventTime = new Date(event.date);
       const timeDiff = eventTime.getTime() - now.getTime();
       
       // Notify 15 minutes before, 5 minutes before, and at event time
       const notificationTimes = [15 * 60 * 1000, 5 * 60 * 1000, 0];
       
       return notificationTimes.some(notifyTime => {
         return Math.abs(timeDiff - notifyTime) < 30000; // 30 second window
       }) && !event.hasBeenNotified;
     }
   }
   ```

3. **In-App Notifications (src/components/notifications/ReminderToast.tsx):**
   ```tsx
   export function ReminderToast({ notification, onSnooze, onDismiss }: ReminderToastProps) {
     return (
       <motion.div
         className="reminder-toast"
         initial={{ opacity: 0, y: 50, scale: 0.9 }}
         animate={{ opacity: 1, y: 0, scale: 1 }}
         exit={{ opacity: 0, y: -50, scale: 0.9 }}
         layout
       >
         <div className="reminder-content">
           <div className="reminder-icon">
             <Bell className="w-5 h-5" />
           </div>
           
           <div className="reminder-text">
             <h4>{notification.title}</h4>
             <p>{notification.message}</p>
             <span className="reminder-time">
               {formatEventTime(notification.event.date)}
             </span>
           </div>
         </div>
         
         <div className="reminder-actions">
           <button
             onClick={() => onSnooze(notification.id, 10)}
             className="snooze-btn"
           >
             Snooze 10min
           </button>
           
           <button
             onClick={() => onDismiss(notification.id)}
             className="dismiss-btn"
           >
             Dismiss
           </button>
         </div>
       </motion.div>
     );
   }
   ```

4. **Service Worker (public/sw.js):**
   ```javascript
   // Basic service worker for background notifications
   self.addEventListener('notificationclick', (event) => {
     event.notification.close();
     
     if (event.action === 'snooze') {
       // Schedule snooze reminder
       scheduleSnoozeReminder(event.notification.tag, 10);
     } else if (event.action === 'dismiss') {
       // Mark as dismissed
       markReminderDismissed(event.notification.tag);
     } else {
       // Open app and focus on event
       event.waitUntil(
         clients.openWindow('/').then(client => {
           if (client) {
             client.focus();
             client.postMessage({
               type: 'FOCUS_EVENT',
               eventId: event.notification.tag.replace('event-', '')
             });
           }
         })
       );
     }
   });
   ```

5. **Permission Request Component (src/components/notifications/NotificationPermission.tsx):**
   ```tsx
   export function NotificationPermission() {
     const [permissionState, setPermissionState] = useState<NotificationPermission>('default');
     
     useEffect(() => {
       if ('Notification' in window) {
         setPermissionState(Notification.permission);
       }
     }, []);
     
     const requestPermission = async () => {
       const notificationService = new NotificationService();
       const granted = await notificationService.requestPermission();
       setPermissionState(granted ? 'granted' : 'denied');
     };
     
     if (permissionState === 'granted') return null;
     
     return (
       <motion.div
         className="permission-banner"
         initial={{ opacity: 0, y: -20 }}
         animate={{ opacity: 1, y: 0 }}
       >
         <div className="permission-content">
           <Bell className="w-5 h-5" />
           <span>Enable notifications to get reminders for your events</span>
         </div>
         
         <button onClick={requestPermission} className="enable-btn">
           Enable Notifications
         </button>
       </motion.div>
     );
   }
   ```

6. **Integration Updates:**
   - Start reminder scheduler on app initialization
   - Add notification permission banner to dashboard
   - Connect snooze/dismiss actions with event updates
   - Update event responses to mention notification setup

**NOTIFICATION STRATEGY:**
- Primary: Browser notifications (when permitted)
- Fallback: In-app toast notifications
- Background: Service worker for closed tabs
- Timing: 15 min before, 5 min before, at event time

**USER EXPERIENCE:**
- Graceful permission request flow
- Clear fallback when notifications disabled
- Snooze functionality with multiple duration options
- Visual feedback for all notification actions

**COMPLETION REQUIREMENTS:**
1. Browser notifications work when permissions granted
2. In-app notifications provide fallback option
3. Reminder scheduling checks upcoming events accurately
4. Snooze and dismiss functions work properly
5. Service worker handles background notifications

**DELIVERABLE:**
Update `/docs/phase-05-completion.md` with reminder system section:
- Notification service architecture
- Permission handling strategy
- Reminder scheduling implementation
- In-app notification system
- Service worker functionality
- Phase 6 readiness (database search implementation)
```

---

## Phase 6: Database Search Implementation

### Research Prompt 6.1 - Semantic Search Research
```
Using context7 MCP and fetch tool, research extensively:
1. Langsearch API documentation for database search
2. Semantic search implementation patterns
3. Vector embeddings for search (if needed)
4. Search result ranking algorithms
5. Faceted search implementation
6. Search query optimization
7. Caching strategies for search results
8. Search analytics and improvements

Save to /research/11-semantic-search.md with:
- Complete Langsearch integration guide
- Search algorithm implementations
- Performance optimization strategies
```

### Main Prompt 6.1 - Database Search Foundation
```
**CONTEXT: AI Dashboard App - Phase 6.1 Database Search Foundation**
You're implementing the database search capability that allows users to query their tasks and events with natural language like "How many tasks do I have?" or "What meetings do I have this week?"

**PREREQUISITES:**
1. MUST read /research/11-semantic-search.md completely
2. MUST read /docs/phase-05-completion.md to understand current system state
3. Verify these exist from previous phases:
   - Task and Event databases with data
   - Tool calling system with DatabaseSearchTool skeleton
   - Enhanced response generation system
   - UI animation system ready for search feedback

**DATABASE SEARCH CONTEXT:**
Users will ask questions like:
- "How many tasks do I have left?"
- "What meetings do I have this week?"
- "Show me all homework tasks"
- "Do I have anything scheduled for tomorrow?"

**PRIMARY OBJECTIVE:**
Set up Langsearch integration and implement database search foundation.

**FILES TO CREATE:**

1. **Langsearch Integration (src/services/search/):**
   - `langsearchClient.ts`: Langsearch API client
   - `databaseIndexer.ts`: Index tasks and events for search
   - `searchService.ts`: Main search orchestration
   - `queryProcessor.ts`: Process natural language queries

2. **Enhanced DatabaseSearchTool:**
   - Query understanding and scope determination
   - Search execution via Langsearch
   - Result formatting and ranking

3. **Search Result Processing:**
   - Result aggregation and formatting
   - Answer synthesis from search results

**IMPLEMENTATION REQUIREMENTS:**

1. **Langsearch Client (src/services/search/langsearchClient.ts):**
   ```tsx
   export class LangsearchClient {
     private apiKey: string;
     private baseUrl: string;
     
     constructor(apiKey: string) {
       this.apiKey = apiKey;
       this.baseUrl = 'https://api.langsearch.com'; // Adjust based on research
     }
     
     async indexDocument(document: IndexDocument): Promise<IndexResult> {
       // Index tasks/events for searchability
       // Include title, description, date, type, etc.
       try {
         const response = await fetch(`${this.baseUrl}/index`, {
           method: 'POST',
           headers: {
             'Authorization': `Bearer ${this.apiKey}`,
             'Content-Type': 'application/json'
           },
           body: JSON.stringify(document)
         });
         
         return await response.json();
       } catch (error) {
         throw new Error(`Indexing failed: ${error.message}`);
       }
     }
     
     async search(query: SearchQuery): Promise<SearchResult[]> {
       // Execute semantic search query
       // Support filtering by type, date range, etc.
       try {
         const response = await fetch(`${this.baseUrl}/search`, {
           method: 'POST',
           headers: {
             'Authorization': `Bearer ${this.apiKey}`,
             'Content-Type': 'application/json'
           },
           body: JSON.stringify(query)
         });
         
         return await response.json();
       } catch (error) {
         throw new Error(`Search failed: ${error.message}`);
       }
     }
   }
   ```

2. **Database Indexer (src/services/search/databaseIndexer.ts):**
   ```tsx
   export class DatabaseIndexer {
     private langsearch: LangsearchClient;
     
     constructor(langsearchClient: LangsearchClient) {
       this.langsearch = langsearchClient;
     }
     
     async indexTask(task: Task): Promise<void> {
       const document: IndexDocument = {
         id: `task-${task.id}`,
         type: 'task',
         title: task.title,
         content: task.description || task.title,
         metadata: {
           completed: task.completed,
           createdAt: task.createdAt,
           updatedAt: task.updatedAt,
           order: task.order
         }
       };
       
       await this.langsearch.indexDocument(document);
     }
     
     async indexEvent(event: Event): Promise<void> {
       const document: IndexDocument = {
         id: `event-${event.id}`,
         type: 'event',
         title: event.title,
         content: event.description || event.title,
         metadata: {
           date: event.date,
           isAllDay: event.isAllDay,
           isReminder: event.isReminder,
           createdAt: event.createdAt
         }
       };
       
       await this.langsearch.indexDocument(document);
     }
     
     async reindexAll(): Promise<void> {
       // Reindex all tasks and events
       const [tasks, events] = await Promise.all([
         taskService.getAll(),
         eventService.getAll()
       ]);
       
       // Index in batches to avoid rate limits
       for (const task of tasks) {
         await this.indexTask(task);
       }
       
       for (const event of events) {
         await this.indexEvent(event);
       }
     }
   }
   ```

3. **Enhanced DatabaseSearchTool (src/services/llm/tools/DatabaseSearchTool.ts):**
   ```tsx
   export const DatabaseSearchTool: Tool = {
     name: 'database_search',
     description: 'Search user tasks and events using natural language',
     parameters: {
       type: 'object',
       properties: {
         query: { type: 'string', description: 'Search query' },
         scope: { 
           type: 'string', 
           enum: ['tasks', 'events', 'both'],
           description: 'What to search: tasks, events, or both' 
         },
         filters: {
           type: 'object',
           properties: {
             completed: { type: 'boolean', description: 'Filter by completion status' },
             dateRange: { 
               type: 'object',
               properties: {
                 start: { type: 'string', description: 'Start date' },
                 end: { type: 'string', description: 'End date' }
               }
             }
           }
         }
       },
       required: ['query', 'scope']
     },
     
     execute: async (params) => {
       try {
         const searchService = new SearchService();
         
         // Process the natural language query
         const processedQuery = await searchService.processQuery(
           params.query,
           params.scope,
           params.filters
         );
         
         // Execute search via Langsearch
         const results = await searchService.search(processedQuery);
         
         // Format results for LLM consumption
         const formattedResults = await searchService.formatResults(results);
         
         return {
           success: true,
           data: {
             results: formattedResults,
             totalCount: results.length,
             query: params.query,
             scope: params.scope
           },
           message: `Found ${results.length} results for "${params.query}"`
         };
         
       } catch (error) {
         return {
           success: false,
           error: error.message,
           message: 'Search failed. Please try rephrasing your query.'
         };
       }
     }
   };
   ```

4. **Search Service (src/services/search/searchService.ts):**
   ```tsx
   export class SearchService {
     private langsearch: LangsearchClient;
     private indexer: DatabaseIndexer;
     
     constructor() {
       this.langsearch = new LangsearchClient(process.env.LANGSEARCH_API_KEY!);
       this.indexer = new DatabaseIndexer(this.langsearch);
     }
     
     async processQuery(
       query: string, 
       scope: string, 
       filters?: any
     ): Promise<ProcessedQuery> {
       // Analyze query intent
       // Extract keywords and semantic meaning
       // Determine optimal search strategy
       
       return {
         originalQuery: query,
         semanticQuery: await this.generateSemanticQuery(query),
         scope,
         filters: filters || {},
         searchType: this.determineSearchType(query)
       };
     }
     
     async search(processedQuery: ProcessedQuery): Promise<SearchResult[]> {
       // Execute search via Langsearch
       const searchQuery: SearchQuery = {
         query: processedQuery.semanticQuery,
         filters: {
           type: processedQuery.scope === 'both' ? undefined : processedQuery.scope,
           ...processedQuery.filters
         },
         limit: 50,
         includeMetadata: true
       };
       
       const results = await this.langsearch.search(searchQuery);
       
       // Post-process and rank results
       return this.rankResults(results, processedQuery);
     }
     
     async formatResults(results: SearchResult[]): Promise<FormattedResult[]> {
       // Format search results for LLM consumption
       return results.map(result => ({
         id: result.id,
         type: result.type,
         title: result.title,
         content: result.content,
         relevanceScore: result.score,
         metadata: result.metadata,
         summary: this.generateResultSummary(result)
       }));
     }
   }
   ```

5. **Integration with UI Animations:**
   Update ResponseArea to show search progress:
   ```tsx
   // In ResponseArea component
   const searchAnimationSteps = [
     "Using semantic database tool...",
     "Searching for matching items...",
     `Found ${resultCount} results`,
     "Synthesizing answer..."
   ];
   ```

6. **Auto-indexing Integration:**
   Update task and event services to auto-index on CRUD operations:
   ```tsx
   // In taskService.create()
   const task = await prisma.task.create(data);
   await searchIndexer.indexTask(task); // Auto-index new tasks
   return task;
   ```

**SEARCH SCOPE INTELLIGENCE:**
The AI should intelligently determine search scope:
- "How many tasks?" → scope: 'tasks'
- "What meetings this week?" → scope: 'events' + date filter
- "What do I have to do today?" → scope: 'both' + today filter

**INDEXING STRATEGY:**
- Index all existing data on first setup
- Auto-index new items on creation
- Re-index on updates
- Remove from index on deletion

**COMPLETION REQUIREMENTS:**
1. Langsearch integration works with API
2. Tasks and events are properly indexed
3. DatabaseSearchTool executes searches successfully
4. Search results are formatted for LLM processing
5. Auto-indexing works for new/updated items

**DELIVERABLE:**
Create `/docs/phase-06-completion.md` documenting:
- Langsearch integration setup and configuration
- Database indexing strategy and implementation
- Search service architecture
- DatabaseSearchTool functionality
- Auto-indexing integration with CRUD operations
- Phase 6.2 readiness (search UI and result synthesis)
```

### Research Prompt 6.2 - Search UI/UX Research
```
Using context7 MCP and fetch tool, research:
1. Search result display patterns
2. Search animation best practices
3. Loading states for search
4. No results handling
5. Search suggestions and autocomplete
6. Search history UI patterns
7. Advanced search interfaces

Save to /research/12-search-ui-ux.md
```

### Main Prompt 6.2 - Database Search Flow Completion
```
**CONTEXT: AI Dashboard App - Phase 6.2 Database Search Flow**
Building on the search foundation from Phase 6.1, you're completing the database search user experience with result synthesis and UI integration.

**PREREQUISITES:**
1. MUST read /research/12-search-ui-ux.md completely
2. MUST read /docs/phase-06-completion.md to understand search foundation
3. Verify these work from Phase 6.1:
   - Langsearch integration and indexing
   - DatabaseSearchTool execution
   - Auto-indexing on CRUD operations
   - Search result formatting

**SEARCH FLOW CONTEXT:**
Complete user experience:
1. User: "How many tasks do I have left?"
2. AI categorizes as QUESTION → Database search
3. UI shows: "Using semantic database tool - Searching for tasks" → "Found 12 results" → "Synthesizing result..."
4. AI synthesizes: "You have 8 incomplete tasks remaining: 'Biology homework', 'Buy groceries'..." etc.
5. Results optionally display as expandable cards

**PRIMARY OBJECTIVE:**
Complete the database search flow with result synthesis and enhanced UI.

**FILES TO CREATE/ENHANCE:**

1. **Result Synthesis (src/services/llm/synthesis/):**
   - `resultSynthesizer.ts`: Convert search results to natural language
   - `answerGenerator.ts`: Generate contextual answers from results

2. **Search UI Components:**
   - `SearchResultCard.tsx`: Display individual search results
   - `SearchProgress.tsx`: Animated search progress indicator

3. **Enhanced Search Integration:**
   - Improve search animations and feedback
   - Add result display options
   - Implement search result caching

**IMPLEMENTATION REQUIREMENTS:**

1. **Result Synthesizer (src/services/llm/synthesis/resultSynthesizer.ts):**
   ```tsx
   export class ResultSynthesizer {
     async synthesizeAnswer(
       query: string,
       results: FormattedResult[],
       searchScope: string
     ): Promise<SynthesizedAnswer> {
       
       // Determine answer type based on query
       const answerType = this.classifyAnswerType(query);
       
       switch (answerType) {
         case 'count':
           return this.generateCountAnswer(query, results);
         
         case 'list':
           return this.generateListAnswer(query, results);
         
         case 'summary':
           return this.generateSummaryAnswer(query, results);
         
         case 'specific':
           return this.generateSpecificAnswer(query, results);
         
         default:
           return this.generateGenericAnswer(query, results);
       }
     }
     
     private generateCountAnswer(query: string, results: FormattedResult[]): SynthesizedAnswer {
       const total = results.length;
       const byType = this.groupResultsByType(results);
       
       let answer = `You have ${total} items`;
       
       if (query.toLowerCase().includes('task')) {
         const taskCount = byType.task?.length || 0;
         const completed = byType.task?.filter(t => t.metadata.completed).length || 0;
         answer = `You have ${taskCount} tasks total, with ${taskCount - completed} remaining to complete.`;
       } else if (query.toLowerCase().includes('event') || query.toLowerCase().includes('meeting')) {
         const eventCount = byType.event?.length || 0;
         answer = `You have ${eventCount} upcoming events.`;
       }
       
       return {
         text: answer,
         results: results.slice(0, 5), // Show top 5 for context
         metadata: { type: 'count', total, byType }
       };
     }
     
     private generateListAnswer(query: string, results: FormattedResult[]): SynthesizedAnswer {
       if (results.length === 0) {
         return {
           text: "I couldn't find any items matching your search.",
           results: [],
           metadata: { type: 'list', empty: true }
         };
       }
       
       const items = results.slice(0, 10).map(r => r.title).join(', ');
       const answer = `Here's what I found: ${items}${results.length > 10 ? ` and ${results.length - 10} more items` : ''}.`;
       
       return {
         text: answer,
         results: results.slice(0, 10),
         metadata: { type: 'list', total: results.length }
       };
     }
   }
   ```

2. **Enhanced DatabaseSearchTool Integration:**
   ```tsx
   // Update DatabaseSearchTool execute method
   execute: async (params) => {
     try {
       const searchService = new SearchService();
       const synthesizer = new ResultSynthesizer();
       
       // Execute search
       const processedQuery = await searchService.processQuery(
         params.query,
         params.scope,
         params.filters
       );
       
       const results = await searchService.search(processedQuery);
       const formattedResults = await searchService.formatResults(results);
       
       // Synthesize natural language answer
       const synthesizedAnswer = await synthesizer.synthesizeAnswer(
         params.query,
         formattedResults,
         params.scope
       );
       
       return {
         success: true,
         data: {
           answer: synthesizedAnswer.text,
           results: synthesizedAnswer.results,
           metadata: synthesizedAnswer.metadata,
           originalQuery: params.query
         },
         message: synthesizedAnswer.text
       };
       
     } catch (error) {
       return {
         success: false,
         error: error.message,
         message: 'Search failed. Please try rephrasing your query.'
       };
     }
   }
   ```

3. **Search Progress Component (src/components/dashboard/SearchProgress.tsx):**
   ```tsx
   export function SearchProgress({ step, query, resultCount }: SearchProgressProps) {
     const progressSteps = [
       { key: 'searching', text: `Using semantic database tool - Searching for ${query}` },
       { key: 'found', text: `Found ${resultCount} results` },
       { key: 'synthesizing', text: 'Synthesizing result...' }
     ];
     
     const currentStepIndex = progressSteps.findIndex(s => s.key === step);
     
     return (
       <motion.div className="search-progress">
         {progressSteps.map((progressStep, index) => (
           <motion.div
             key={progressStep.key}
             className={cn(
               'progress-step',
               index <= currentStepIndex && 'active',
               index < currentStepIndex && 'completed'
             )}
             initial={{ opacity: 0, x: -20 }}
             animate={{ 
               opacity: index <= currentStepIndex ? 1 : 0.3,
               x: 0 
             }}
             transition={{ delay: index * 0.2 }}
           >
             <div className="step-indicator">
               {index < currentStepIndex ? (
                 <Check className="w-4 h-4" />
               ) : index === currentStepIndex ? (
                 <Loader className="w-4 h-4 animate-spin" />
               ) : (
                 <div className="step-dot" />
               )}
             </div>
             <span className="step-text">{progressStep.text}</span>
           </motion.div>
         ))}
       </motion.div>
     );
   }
   ```

4. **Search Result Display (src/components/dashboard/SearchResultCard.tsx):**
   ```tsx
   export function SearchResultCard({ result, onExpand }: SearchResultCardProps) {
     const [isExpanded, setIsExpanded] = useState(false);
     
     return (
       <motion.div
         className="search-result-card"
         layout
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         whileHover={{ scale: 1.02 }}
       >
         <div className="result-header">
           <div className="result-type-badge">
             {result.type === 'task' ? <CheckSquare /> : <Calendar />}
             {result.type}
           </div>
           <div className="result-score">
             {Math.round(result.relevanceScore * 100)}% match
           </div>
         </div>
         
         <h4 className="result-title">{result.title}</h4>
         
         {result.content && result.content !== result.title && (
           <p className="result-content">
             {isExpanded ? result.content : `${result.content.slice(0, 100)}...`}
           </p>
         )}
         
         <div className="result-metadata">
           {result.type === 'task' && (
             <span className={cn('status', result.metadata.completed && 'completed')}>
               {result.metadata.completed ? 'Completed' : 'Pending'}
             </span>
           )}
           {result.type === 'event' && (
             <span className="event-date">
               {formatDate(result.metadata.date)}
             </span>
           )}
         </div>
         
         {result.content && result.content.length > 100 && (
           <button
             onClick={() => setIsExpanded(!isExpanded)}
             className="expand-btn"
           >
             {isExpanded ? 'Show less' : 'Show more'}
           </button>
         )}
       </motion.div>
     );
   }
   ```

5. **Enhanced Response Integration:**
   Update the main AI flow to show search results optionally:
   ```tsx
   // In main AI processing flow
   if (toolResult.success && toolResult.data.results?.length > 0) {
     // Stream the synthesized answer
     await streamResponse(toolResult.message);
     
     // Show expandable results section
     showSearchResults(toolResult.data.results);
   }
   ```

6. **Search History Integration:**
   Update RecentSearches to show database searches:
   ```tsx
   // Enhanced RecentSearches component
   const addToSearchHistory = (query: string, type: 'database' | 'web', resultCount: number) => {
     searchHistoryStore.addSearch({
       query,
       type,
       resultCount,
       timestamp: new Date()
     });
   };
   ```

**ANSWER QUALITY REQUIREMENTS:**
- Count queries: Provide specific numbers and breakdowns
- List queries: Show relevant items with context
- Summary queries: Synthesize key information
- Handle empty results gracefully with suggestions

**UI ANIMATION INTEGRATION:**
- Show search progress with step-by-step animations
- Display result cards with staggered entrance
- Smooth transitions between search states
- Loading states that match the AI processing flow

**COMPLETION REQUIREMENTS:**
1. Database search flow works end-to-end with natural responses
2. Search results are synthesized into helpful answers
3. UI shows appropriate progress and result displays
4. Search history is properly tracked and displayed
5. Empty results are handled with helpful suggestions

**DELIVERABLE:**
Update `/docs/phase-06-completion.md` with search flow completion:
- Result synthesis implementation and quality
- Search UI components and animations
- Complete database search flow testing
- Integration with existing UI systems
- Phase 7 readiness (web search implementation)
```

---

## Phase 7: Web Search Implementation

### Research Prompt 7.1 - Web Search Integration Research
```
Using context7 MCP and fetch tool, research:
1. Langsearch web search API documentation
2. Web scraping best practices
3. Search result quality assessment
4. URL handling and validation
5. Content extraction patterns
6. Search result caching strategies
7. Rate limiting for web searches
8. Legal considerations for web search

Save to /research/13-web-search-integration.md with:
- Complete API integration examples
- Content extraction strategies
- Performance optimization
```

### Main Prompt 7.1 - Web Search Foundation
```
**CONTEXT: AI Dashboard App - Phase 7.1 Web Search Foundation**
You're implementing web search capabilities for general knowledge queries like "What is the best mouse in 2025?" that require external information.

**PREREQUISITES:**
1. MUST read /research/13-web-search-integration.md completely
2. MUST read /docs/phase-06-completion.md to understand database search implementation
3. Verify these work from previous phases:
   - Tool calling system with WebSearchTool skeleton
   - Search UI components and animations
   - Result synthesis system
   - Recent searches tracking

**WEB SEARCH CONTEXT:**
Users will ask questions that require web information:
- "What is the best mouse and keyboard in 2025?"
- "Latest news about AI developments"
- "How to fix a leaky faucet?"
- "Weather forecast for next week"

**PRIMARY OBJECTIVE:**
Implement Langsearch web search integration with result processing.

**FILES TO CREATE:**

1. **Web Search Service (src/services/search/webSearch/):**
   - `webSearchClient.ts`: Langsearch web search API client
   - `webSearchService.ts`: Main web search orchestration
   - `resultProcessor.ts`: Process and filter web search results
   - `contentExtractor.ts`: Extract relevant content from results

2. **Enhanced WebSearchTool:**
   - Query optimization for web search
   - Result count determination
   - Source credibility assessment

3. **Web Result Synthesis:**
   - Multi-source information combining
   - Citation formatting
   - Fact verification patterns

**IMPLEMENTATION REQUIREMENTS:**

1. **Web Search Client (src/services/search/webSearch/webSearchClient.ts):**
   ```tsx
   export class WebSearchClient {
     private apiKey: string;
     private baseUrl: string;
     
     constructor(apiKey: string) {
       this.apiKey = apiKey;
       this.baseUrl = 'https://api.langsearch.com'; // Based on research
     }
     
     async searchWeb(query: WebSearchQuery): Promise<WebSearchResult[]> {
       try {
         const response = await fetch(`${this.baseUrl}/web-search`, {
           method: 'POST',
           headers: {
             'Authorization': `Bearer ${this.apiKey}`,
             'Content-Type': 'application/json'
           },
           body: JSON.stringify({
             query: query.searchQuery,
             num_results: query.resultCount || 10,
             country: query.country || 'US',
             language: query.language || 'en',
             safe_search: query.safeSearch || 'moderate',
             time_filter: query.timeFilter // recent, past_year, etc.
           })
         });
         
         if (!response.ok) {
           throw new Error(`Web search failed: ${response.statusText}`);
         }
         
         const results = await response.json();
         return this.processRawResults(results);
         
       } catch (error) {
         throw new Error(`Web search error: ${error.message}`);
       }
     }
     
     private processRawResults(rawResults: any[]): WebSearchResult[] {
       return rawResults.map(result => ({
         id: result.id || generateId(),
         title: result.title,
         url: result.url,
         snippet: result.snippet,
         content: result.content || result.snippet,
         domain: this.extractDomain(result.url),
         publishedDate: result.published_date,
         relevanceScore: result.score || 0,
         metadata: {
           author: result.author,
           wordCount: result.content?.length || 0,
           language: result.language
         }
       }));
     }
   }
   ```

2. **Enhanced WebSearchTool (src/services/llm/tools/WebSearchTool.ts):**
   ```tsx
   export const WebSearchTool: Tool = {
     name: 'web_search',
     description: 'Search the web for current information and general knowledge',
     parameters: {
       type: 'object',
       properties: {
         query: { type: 'string', description: 'Search query for the web' },
         resultCount: { 
           type: 'number', 
           description: 'Number of results to fetch (5-20)',
           minimum: 5,
           maximum: 20
         },
         timeFilter: {
           type: 'string',
           enum: ['any_time', 'past_day', 'past_week', 'past_month', 'past_year'],
           description: 'Time filter for results'
         }
       },
       required: ['query']
     },
     
     execute: async (params) => {
       try {
         const webSearchService = new WebSearchService();
         
         // Optimize the search query
         const optimizedQuery = await webSearchService.optimizeQuery(params.query);
         
         // Determine appropriate result count based on query complexity
         const resultCount = params.resultCount || 
           webSearchService.determineOptimalResultCount(params.query);
         
         // Execute web search
         const searchQuery: WebSearchQuery = {
           searchQuery: optimizedQuery,
           resultCount,
           timeFilter: params.timeFilter || 'any_time',
           safeSearch: 'moderate'
         };
         
         const results = await webSearchService.search(searchQuery);
         
         // Filter and rank results
         const processedResults = await webSearchService.processResults(results, params.query);
         
         // Track search for history
         await addToSearchHistory(params.query, 'web', processedResults.length);
         
         return {
           success: true,
           data: {
             results: processedResults,
             originalQuery: params.query,
             optimizedQuery,
             resultCount: processedResults.length,
             sources: processedResults.map(r => ({ title: r.title, url: r.url }))
           },
           message: `Found ${processedResults.length} web results for "${params.query}"`
         };
         
       } catch (error) {
         return {
           success: false,
           error: error.message,
           message: 'Web search failed. Please try a different query.'
         };
       }
     }
   };
   ```

3. **Web Search Service (src/services/search/webSearch/webSearchService.ts):**
   ```tsx
   export class WebSearchService {
     private client: WebSearchClient;
     private processor: ResultProcessor;
     private extractor: ContentExtractor;
     
     constructor() {
       this.client = new WebSearchClient(process.env.LANGSEARCH_API_KEY!);
       this.processor = new ResultProcessor();
       this.extractor = new ContentExtractor();
     }
     
     async optimizeQuery(query: string): Promise<string> {
       // Enhance query for better web search results
       // Remove personal references ("my", "I have")
       // Add relevant keywords
       // Handle typos and synonyms
       
       let optimized = query
         .replace(/\b(my|I have|do I|am I)\b/gi, '')
         .trim();
       
       // Add current year for recency
       if (query.toLowerCase().includes('best') || query.toLowerCase().includes('latest')) {
         optimized += ' 2025';
       }
       
       return optimized;
     }
     
     determineOptimalResultCount(query: string): number {
       // Simple queries: fewer results
       // Complex queries: more results for synthesis
       const words = query.split(' ').length;
       
       if (words <= 3) return 8;
       if (words <= 6) return 12;
       return 15;
     }
     
     async search(query: WebSearchQuery): Promise<WebSearchResult[]> {
       const results = await this.client.searchWeb(query);
       return results;
     }
     
     async processResults(results: WebSearchResult[], originalQuery: string): Promise<ProcessedWebResult[]> {
       // Filter out low-quality results
       const filtered = results.filter(result => 
         result.relevanceScore > 0.3 &&
         result.snippet.length > 50 &&
         !this.isLowQualityDomain(result.domain)
       );
       
       // Extract and enhance content
       const enhanced = await Promise.all(
         filtered.map(async result => ({
           ...result,
           extractedContent: await this.extractor.extractKey(result.content, originalQuery),
           credibilityScore: this.assessCredibility(result)
         }))
       );
       
       // Sort by relevance and credibility
       return enhanced.sort((a, b) => 
         (b.relevanceScore * 0.7 + b.credibilityScore * 0.3) -
         (a.relevanceScore * 0.7 + a.credibilityScore * 0.3)
       );
     }
     
     private assessCredibility(result: WebSearchResult): number {
       const domain = result.domain.toLowerCase();
       
       // High credibility domains
       const highCredibility = [
         'wikipedia.org', 'stackoverflow.com', 'github.com',
         'mozilla.org', 'w3.org', 'arxiv.org'
       ];
       
       // Medium credibility patterns
       const mediumCredibility = [
         '.edu', '.gov', '.org'
       ];
       
       if (highCredibility.some(d => domain.includes(d))) return 0.9;
       if (mediumCredibility.some(d => domain.includes(d))) return 0.7;
       
       return 0.5; // Default credibility
     }
   }
   ```

4. **Content Extractor (src/services/search/webSearch/contentExtractor.ts):**
   ```tsx
   export class ContentExtractor {
     async extractKey(content: string, query: string): Promise<string> {
       // Extract most relevant portions of content
       // Focus on sentences that relate to the query
       // Remove boilerplate and navigation text
       
       const sentences = content.split(/[.!?]+/).filter(s => s.length > 20);
       const queryWords = query.toLowerCase().split(' ');
       
       // Score sentences by relevance to query
       const scoredSentences = sentences.map(sentence => {
         const lowerSentence = sentence.toLowerCase();
         const score = queryWords.reduce((acc, word) => {
           return acc + (lowerSentence.includes(word) ? 1 : 0);
         }, 0);
         
         return { sentence: sentence.trim(), score };
       });
       
       // Return top relevant sentences
       return scoredSentences
         .sort((a, b) => b.score - a.score)
         .slice(0, 3)
         .map(s => s.sentence)
         .join('. ');
     }
   }
   ```

5. **Integration with UI:**
   Update search progress animations for web search:
   ```tsx
   // Web search specific progress steps
   const webSearchSteps = [
     "Launching web search...",
     `Searching for "${optimizedQuery}"`,
     `Found ${resultCount} results`,
     "Extracting relevant information...",
     "Synthesizing answer..."
   ];
   ```

**QUERY OPTIMIZATION STRATEGY:**
- Remove personal pronouns and references
- Add year for recency when appropriate
- Handle common typos and synonyms
- Optimize for search engine understanding

**RESULT QUALITY CONTROL:**
- Filter out low-relevance results (< 0.3 score)
- Assess source credibility
- Extract key content related to query
- Limit results to prevent information overload

**COMPLETION REQUIREMENTS:**
1. Langsearch web search API integration works
2. Query optimization improves search results
3. Result filtering provides quality sources
4. Content extraction focuses on relevant information
5. Integration with existing search UI animations

**DELIVERABLE:**
Create `/docs/phase-07-completion.md` documenting:
- Web search API integration and configuration
- Query optimization strategies implemented
- Result processing and quality filtering
- Content extraction effectiveness
- Phase 7.2 readiness (result synthesis and source attribution)
```

### Research Prompt 7.2 - Search Result Synthesis Research
```
Using context7 MCP and fetch tool, research:
1. LLM summarization best practices
2. Multi-document synthesis techniques
3. Citation formatting patterns
4. Fact verification strategies
5. Result quality assessment
6. Bias detection and mitigation

Save to /research/14-search-synthesis.md
```

### Main Prompt 7.2 - Web Search Flow Completion
```
**CONTEXT: AI Dashboard App - Phase 7.2 Web Search Flow Completion**
Building on web search foundation from Phase 7.1, you're completing the web search experience with intelligent result synthesis, source attribution, and enhanced UI.

**PREREQUISITES:**
1. MUST read /research/14-search-synthesis.md completely
2. MUST read /docs/phase-07-completion.md to understand web search foundation
3. Verify these work from Phase 7.1:
   - Langsearch web search integration
   - WebSearchTool execution
   - Query optimization and result filtering
   - Content extraction from web results

**WEB SEARCH FLOW CONTEXT:**
Complete user experience:
1. User: "What is the best mouse and keyboard in 2025?"
2. AI categorizes as QUESTION → Web search
3. UI shows: "Launching web search" → "Searching for 'best mouse keyboard 2025'" → "Found 12 results" → "Synthesizing answer..."
4. AI synthesizes information from multiple sources
5. Response includes natural language answer + expandable source list
6. Recent searches updated with web search entry

**PRIMARY OBJECTIVE:**
Complete web search flow with intelligent synthesis and source attribution.

**FILES TO CREATE/ENHANCE:**

1. **Web Result Synthesizer (src/services/llm/synthesis/webResultSynthesizer.ts):**
   - Multi-source information combining
   - Fact cross-referencing
   - Natural language answer generation

2. **Source Attribution (src/components/dashboard/SourceAttribution.tsx):**
   - Expandable source list with previews
   - Link validation and safety
   - Source credibility indicators

3. **Enhanced Web Search Integration:**
   - Complete flow with animations
   - Error handling and fallbacks
   - Search result caching

**IMPLEMENTATION REQUIREMENTS:**

1. **Web Result Synthesizer (src/services/llm/synthesis/webResultSynthesizer.ts):**
   ```tsx
   export class WebResultSynthesizer {
     async synthesizeWebAnswer(
       query: string,
       results: ProcessedWebResult[]
     ): Promise<WebSynthesizedAnswer> {
       
       if (results.length === 0) {
         return {
           text: "I couldn't find any relevant information for your query. Try rephrasing or being more specific.",
           sources: [],
           confidence: 0,
           metadata: { type: 'no_results' }
         };
       }
       
       // Group information by topic/theme
       const informationClusters = this.clusterInformation(results);
       
       // Generate synthesis based on query type
       const answerType = this.classifyWebQuery(query);
       
       switch (answerType) {
         case 'recommendation':
           return this.generateRecommendationAnswer(query, informationClusters, results);
         
         case 'explanation':
           return this.generateExplanationAnswer(query, informationClusters, results);
         
         case 'factual':
           return this.generateFactualAnswer(query, informationClusters, results);
         
         case 'comparison':
           return this.generateComparisonAnswer(query, informationClusters, results);
         
         default:
           return this.generateGenericAnswer(query, informationClusters, results);
       }
     }
     
     private generateRecommendationAnswer(
       query: string,
       clusters: InformationCluster[],
       results: ProcessedWebResult[]
     ): WebSynthesizedAnswer {
       
       // Extract top recommendations from multiple sources
       const recommendations = this.extractRecommendations(clusters);
       
       let answer = `Based on current reviews and expert opinions, here are the top recommendations:\n\n`;
       
       recommendations.slice(0, 3).forEach((rec, index) => {
         answer += ` "